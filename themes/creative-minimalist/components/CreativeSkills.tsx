"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { Skill } from "@/lib/types";
import { Plus, Trash2 } from "lucide-react";

interface SkillItemProps {
    skill: Skill;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Skill, value: string) => void;
    onDelete: (id: string) => void;
}

const SkillItem = ({ skill, isEditing, onUpdate, onDelete }: SkillItemProps) => {
    return (
        <div className="skill-badge">
            {isEditing ? (
                <div className="skill-badge-edit">
                    <input
                        type="text"
                        value={skill.name}
                        onChange={(e) => onUpdate(skill.id, 'name', e.target.value)}
                        className="skill-badge-input"
                        placeholder="Enter skill name"
                    />
                    <button
                        onClick={() => onDelete(skill.id)}
                        className="skill-badge-delete"
                        title="Delete skill"
                    >
                        <Trash2 className="skill-badge-delete-icon" />
                    </button>
                </div>
            ) : (
                <span className="skill-badge-text">{skill.name}</span>
            )}
        </div>
    );
};

export function CreativeSkills({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddSkill = () => {
        if (dispatch) {
            const newSkill: Skill = {
                id: Date.now().toString(),
                name: 'New Skill'
            };

            // If we only have default skills, replace them with the new one
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.length === 0
                ? [newSkill]
                : [...currentSkills, newSkill];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleUpdateSkill = (id: string, field: keyof Skill, value: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.map(skill =>
                skill.id === id ? { ...skill, [field]: value } : skill
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleDeleteSkill = (id: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.filter(skill => skill.id !== id);
            // When deleting, always update the actual data (even if it becomes empty)
            // The display logic will handle showing dummy when needed
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };


    const skills = data.skills || [];

    // Don't render if no skills and not editing
    if (!isEditing && skills.length === 0) {
        return null;
    }

    return (
        <section id="skills" className="skills-section">
            <div className="skills-container">
                <div className="skills-header">
                    <h2 className="skills-title">Skills & Technologies</h2>
                    <div className="skills-subtitle">
                        Technologies and tools I work with
                    </div>
                </div>

                <div className="skills-content">
                    <div className="skills-badges-grid">
                        {skills.map((skill) => (
                            <SkillItem
                                key={skill.id}
                                skill={skill}
                                isEditing={isEditing}
                                onUpdate={handleUpdateSkill}
                                onDelete={handleDeleteSkill}
                            />
                        ))}
                    </div>

                    {isEditing && (
                        <div className="skills-add-container">
                            <button
                                onClick={handleAddSkill}
                                className="skills-add-btn"
                            >
                                <Plus className="skills-add-icon" />
                                Add New Skill
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
