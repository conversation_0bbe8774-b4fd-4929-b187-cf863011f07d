"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { useEffect } from "react";

// Helper function to strip HTML tags - consistent server/client rendering
const stripHtml = (html: string): string => {
    if (!html) return '';

    // Use regex to strip HTML tags - works consistently on server and client
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')    // Normalize whitespace
        .trim();
};

export function CreativeAbout({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    // Clean existing HTML data on component mount
    useEffect(() => {
        if (isEditing && dispatch && data.bio) {
            const cleanBio = stripHtml(data.bio);
            if (cleanBio !== data.bio) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'bio', value: cleanBio } });
            }
        }
        if (isEditing && dispatch && data.qualifications) {
            const cleanQualifications = stripHtml(data.qualifications);
            if (cleanQualifications !== data.qualifications) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'qualifications', value: cleanQualifications } });
            }
        }
    }, [isEditing, dispatch, data.bio, data.qualifications]);

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    return (
        <section id="about" className="theme-creative-about">
            <div className="theme-creative-about-container">
                <div className="theme-creative-about-header">
                    <h2 className="theme-creative-about-title">About Me</h2>
                    <div className="theme-creative-about-divider"></div>
                </div>

                <div className="theme-creative-about-layout">
                    <div className="theme-creative-about-description">
                        <h3 className="theme-creative-about-section-title">Who I Am</h3>
                        <EditableText
                            isEditing={isEditing}
                            tagName="p"
                            className={`theme-creative-about-text ${isEditing ? 'editable-field-large editable-field-creative' : ''}`}
                            initialValue={data.bio || "I'm passionate about creating meaningful digital experiences that solve real problems. With a focus on clean design and thoughtful user interactions, I bring ideas to life through code and creativity."}
                            placeholder="Tell visitors about yourself, your background, and what drives you"
                            onSave={(value) => handleUpdate('bio', value)}
                        />
                    </div>

                    <div className="theme-creative-about-qualifications">
                        <h3 className="theme-creative-about-section-title">My Qualifications</h3>
                        <div className="theme-creative-about-qualifications-list">
                            <div className="theme-creative-about-qualification-item">
                                <span className="theme-creative-about-qualification-bullet">•</span>
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="span"
                                    className={`theme-creative-about-qualification-text ${isEditing ? 'editable-field editable-field-creative' : ''}`}
                                    initialValue={data.qualification1 || "Bachelor's Degree in Computer Science"}
                                    placeholder="First qualification"
                                    onSave={(value) => handleUpdate('qualification1', value)}
                                />
                            </div>
                            <div className="theme-creative-about-qualification-item">
                                <span className="theme-creative-about-qualification-bullet">•</span>
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="span"
                                    className={`theme-creative-about-qualification-text ${isEditing ? 'editable-field editable-field-creative' : ''}`}
                                    initialValue={data.qualification2 || "5+ years of professional experience"}
                                    placeholder="Second qualification"
                                    onSave={(value) => handleUpdate('qualification2', value)}
                                />
                            </div>
                            {/* <div className="theme-creative-about-qualification-item">
                                <span className="theme-creative-about-qualification-bullet">•</span>
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="span"
                                    className={`theme-creative-about-qualification-text ${isEditing ? 'editable-field editable-field-creative' : ''}`}
                                    initialValue={data.qualification3 || "Certified in modern web technologies"}
                                    placeholder="Third qualification"
                                    onSave={(value) => handleUpdate('qualification3', value)}
                                />
                            </div> */}
                          
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};
