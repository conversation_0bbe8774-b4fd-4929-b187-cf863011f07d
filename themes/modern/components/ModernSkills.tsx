"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { Skill } from "@/lib/types";
import { Plus, Trash2, Zap, Trophy } from "lucide-react";
import { EditableText } from "@/components/ui/EditableText";

interface SkillItemProps {
    skill: Skill;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Skill, value: string) => void;
    onDelete: (id: string) => void;
}


const SkillItem = ({ skill, isEditing, onUpdate, onDelete }: SkillItemProps) => {
    return (
        <div className="modern-skill-badge">
            <div className="modern-skill-badge-content">
                <div className="modern-skill-badge-icon">
                    <Trophy />
                </div>
                <EditableText
                    isEditing={isEditing}
                    tagName="span"
                    className={`modern-skill-badge-text ${isEditing ? 'editable-field-inline editable-field-modern' : ''}`}
                    initialValue={skill.name}
                    placeholder="Skill name"
                    onSave={(value) => onUpdate(skill.id, 'name', value)}
                />
                {isEditing && (
                    <button
                        onClick={() => onDelete(skill.id)}
                        className="modern-skill-badge-delete"
                        title="Delete skill"
                    >
                        <Trash2 size={14} />
                    </button>
                )}
            </div>
        </div>
    );
};

export function ModernSkills({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddSkill = () => {
        if (dispatch) {
            const newSkill: Skill = {
                id: Date.now().toString(),
                name: 'New Skill'
            };

            const currentSkills = data.skills || [];
            const updatedSkills = [...currentSkills, newSkill];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleUpdateSkill = (id: string, field: keyof Skill, value: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.map(skill =>
                skill.id === id ? { ...skill, [field]: value } : skill
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleDeleteSkill = (id: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.filter(skill => skill.id !== id);
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const skills = data.skills || [];

    // Don't render if no skills and not editing
    if (!isEditing && skills.length === 0) {
        return null;
    }

    return (
        <section id="skills" className="modern-skills">
            <div className="modern-skills-container">
                {/* Section Header */}
                <div className="modern-skills-header">
                    <div className="modern-skills-header-icon">
                        <Zap size={32} />
                    </div>
                    <h2 className="modern-skills-title">Skills & Technologies</h2>
                    <div className="modern-skills-divider"></div>
                    <p className="modern-skills-subtitle">
                        Technologies and tools I work with to bring ideas to life
                    </p>
                </div>

                {/* Skills Grid */}
                <div className="modern-skills-content">
                    <div className="modern-skills-grid">
                        {skills.map((skill) => (
                            <SkillItem
                                key={skill.id}
                                skill={skill}
                                isEditing={isEditing}
                                onUpdate={handleUpdateSkill}
                                onDelete={handleDeleteSkill}
                            />
                        ))}

                        {/* Add Skill Button */}
                        {isEditing && (
                            <div className="modern-skills-add-section">
                                <button
                                    onClick={handleAddSkill}
                                    className="modern-skills-add-btn"
                                >
                                    <Plus size={18} />
                                    Add Skill
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </section>
    );
};
