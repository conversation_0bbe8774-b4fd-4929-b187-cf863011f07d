"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { Experience } from "@/lib/types";
import { Plus, Trash2, Briefcase, Clock, MapPin } from "lucide-react";
import { EditableText } from "@/components/ui/EditableText";

interface ExperienceItemProps {
    experience: Experience;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Experience, value: string) => void;
    onDelete: (id: string) => void;
}

const ExperienceItem = ({ experience, isEditing, onUpdate, onDelete }: ExperienceItemProps) => {
    return (
        <div className="modern-experience-card">
            {/* Card Header with Role and Company */}
            <div className="modern-experience-header">
                <div className="modern-experience-role-section">
                    <div className="modern-experience-briefcase-icon">
                        <Briefcase size={20} />
                    </div>
                    <div className="modern-experience-role-content">
                        <EditableText
                            isEditing={isEditing}
                            tagName="h3"
                            className={`modern-experience-role ${isEditing ? 'editable-field editable-field-modern' : ''}`}
                            initialValue={experience.role}
                            placeholder="Job Title"
                            onSave={(value) => onUpdate(experience.id, 'role', value)}
                        />

                        <EditableText
                            isEditing={isEditing}
                            tagName="p"
                            className={`modern-experience-company ${isEditing ? 'editable-field editable-field-modern' : ''}`}
                            initialValue={experience.company}
                            placeholder="Company Name"
                            onSave={(value) => onUpdate(experience.id, 'company', value)}
                        />
                    </div>
                </div>

                {isEditing && (
                    <button
                        onClick={() => onDelete(experience.id)}
                        className="modern-experience-delete-btn"
                        title="Delete Experience"
                    >
                        <Trash2 size={16} />
                    </button>
                )}
            </div>

            {/* Card Meta Information */}
            <div className="modern-experience-meta">
                <div className="modern-experience-duration">
                    <Clock size={16} className="modern-experience-meta-icon" />
                    <EditableText
                        isEditing={isEditing}
                        tagName="span"
                        className={`modern-experience-duration-text ${isEditing ? 'editable-field-inline editable-field-modern' : ''}`}
                        initialValue={experience.duration}
                        placeholder="e.g., Jan 2020 - Present"
                        onSave={(value) => onUpdate(experience.id, 'duration', value)}
                    />
                </div>

                {(experience.location || isEditing) && (
                    <div className="modern-experience-location">
                        <MapPin size={16} className="modern-experience-meta-icon" />
                        <EditableText
                            isEditing={isEditing}
                            tagName="span"
                            className={`modern-experience-location-text ${isEditing ? 'editable-field-inline editable-field-modern' : ''}`}
                            initialValue={experience.location || ''}
                            placeholder="Location (optional)"
                            onSave={(value) => onUpdate(experience.id, 'location', value)}
                        />
                    </div>
                )}
            </div>

            {/* Card Description */}
            {(experience.description || isEditing) && (
                <div className="modern-experience-description">
                    <EditableText
                        isEditing={isEditing}
                        tagName="p"
                        className={`modern-experience-description-text ${isEditing ? 'editable-field-large editable-field-modern' : ''}`}
                        initialValue={experience.description || ''}
                        placeholder="Describe your key responsibilities and achievements in this role..."
                        onSave={(value) => onUpdate(experience.id, 'description', value)}
                    />
                </div>
            )}
        </div>
    );
};

export function ModernExperience({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddExperience = () => {
        if (dispatch) {
            const newExperience: Experience = {
                id: Date.now().toString(),
                role: 'Software Engineer',
                company: 'Tech Company',
                duration: '2023 - Present',
                description: 'Developing innovative solutions and contributing to exciting projects. Click to edit and add your experience details.',
                location: 'Remote'
            };

            const currentExperiences = data.experiences || [];
            const updatedExperiences = [...currentExperiences, newExperience];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const handleUpdateExperience = (id: string, field: keyof Experience, value: string) => {
        if (dispatch) {
            const currentExperiences = data.experiences || [];
            const updatedExperiences = currentExperiences.map(exp =>
                exp.id === id ? { ...exp, [field]: value } : exp
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const handleDeleteExperience = (id: string) => {
        if (dispatch) {
            const currentExperiences = data.experiences || [];
            const updatedExperiences = currentExperiences.filter(exp => exp.id !== id);
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const experiences = data.experiences || [];

    return (
        <section id="experience" className="modern-experience">
            <div className="modern-experience-container">
                {/* Section Header */}
                <div className="modern-experience-header">
                    <h2 className="modern-experience-title">Work Experience</h2>
                    <div className="modern-experience-divider"></div>
                    <p className="modern-experience-subtitle">
                        My professional journey and key achievements
                    </p>
                </div>

                {/* Experience Cards Grid */}
                <div className="modern-experience-grid">
                    {experiences.map((experience) => (
                        <ExperienceItem
                            key={experience.id}
                            experience={experience}
                            isEditing={isEditing}
                            onUpdate={handleUpdateExperience}
                            onDelete={handleDeleteExperience}
                        />
                    ))}
                </div>

                {/* Add Experience Button */}
                {isEditing && (
                    <div className="modern-experience-add-section">
                        <button
                            onClick={handleAddExperience}
                            className="modern-experience-add-btn"
                        >
                            <Plus size={20} />
                            Add Experience
                        </button>
                    </div>
                )}
            </div>
        </section>
    );
};
