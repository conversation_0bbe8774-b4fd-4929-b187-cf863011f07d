"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";

export function ModernFooter({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const currentYear = new Date().getFullYear();

    return (
        <footer className="theme-modern-footer">
            <div className="theme-modern-footer-container">
                <div className="theme-modern-footer-content">
                    <p className="theme-modern-footer-text">
                        {currentYear} {data.userName || "Portfolio"}. All rights reserved.
                    </p>
                    <span className="theme-modern-footer-attribution">
                        Powered by<a className="theme-modern-navbar-footer" href="https://profolify.vercel.app/" target="_blank" rel="noopener noreferrer">
                            <img width="24px" height="24px" src="/icon.svg" alt="Profolify" /> Profolify
                        </a>
                    </span>
                </div>
            </div>
        </footer>
    );
};
