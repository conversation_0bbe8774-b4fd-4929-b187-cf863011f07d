"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Upload, Loader2, Download, FileText } from "lucide-react";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";
import { useEffect } from "react";

// Helper function to strip HTML tags - consistent server/client rendering
const stripHtml = (html: string): string => {
    if (!html) return '';

    // Use regex to strip HTML tags - works consistently on server and client
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')    // Normalize whitespace
        .trim();
};

interface ProfileImageUploaderProps {
    isEditing: boolean;
    data: PortfolioData;
    isUploadingProfile: boolean;
    onImageUpload: (options: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;
}

const ProfileImageUploader = ({ isEditing, data, isUploadingProfile, onImageUpload }: ProfileImageUploaderProps) => (
    <div className="theme-modern-hero-image-container">
        <div className="theme-modern-hero-image-wrapper">
            <PortfolioImage
                isEditing={isEditing}
                src={data.profileImageUrl || 'https://placehold.co/320x320/1a1a1a/ffffff?text=Profile'}
                alt={data.userName}
                width={320}
                height={320}
                className="theme-modern-hero-image"
            />

            {isUploadingProfile && (
                <div className="theme-modern-hero-upload-loading">
                    <div className="theme-modern-hero-upload-loading-content">
                        <Loader2 className="theme-modern-hero-upload-spinner" />
                        <p className="theme-modern-hero-upload-text">Uploading...</p>
                    </div>
                </div>
            )}

            {isEditing && !isUploadingProfile && (
                <label htmlFor="profile-image-upload-modern" className="theme-modern-hero-upload-overlay">
                    <Upload className="theme-modern-hero-upload-icon" />
                    <span>Upload Photo</span>
                    <input 
                        id="profile-image-upload-modern" 
                        type="file" 
                        className="theme-modern-hero-upload-input" 
                        accept="image/*" 
                        onChange={(e) => e.target.files && onImageUpload({ file: e.target.files[0], type: 'profile' })} 
                    />
                </label>
            )}
        </div>
    </div>
);

interface HeroContentProps {
    isEditing: boolean;
    data: PortfolioData;
    handleUpdate: (field: keyof Omit<PortfolioData, 'projects'>, value: string) => void;
    isUploadingResume: boolean;
    onImageUpload: (options: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;
    isExport: boolean;
}

const HeroContent = ({ isEditing, data, handleUpdate, isUploadingResume, onImageUpload }: HeroContentProps) => (
    <div className="theme-modern-hero-content">
        <EditableText
            isEditing={isEditing}
            tagName="h1"
            className={`theme-modern-hero-title ${isEditing ? 'editable-field-gradient editable-field-modern' : ''}`}
            initialValue={data.userName}
            placeholder="Your Name"
            onSave={(v) => handleUpdate('userName', v)}
        />
        <EditableText
            isEditing={isEditing}
            tagName="p"
            className={`theme-modern-hero-subtitle ${isEditing ? 'editable-field editable-field-modern' : ''}`}
            initialValue={data.profession}
            placeholder="Your Profession"
            onSave={(v) => handleUpdate('profession', v)}
        />
        <div className="theme-modern-hero-actions">
            {isEditing && (
                isUploadingResume ? (
                    <button disabled className="theme-modern-btn theme-modern-btn-secondary">
                        <Loader2 className="theme-modern-hero-action-icon theme-modern-spinner" />
                        Uploading...
                    </button>
                ) : (
                    <label className="theme-modern-btn theme-modern-btn-secondary theme-modern-hero-upload-btn">
                        <FileText className="theme-modern-hero-action-icon" />
                        <span>Upload Resume</span>
                        <input 
                            id="resume-upload-modern" 
                            type="file" 
                            className="theme-modern-hero-upload-input" 
                            accept=".pdf" 
                            onChange={(e) => e.target.files && onImageUpload({ file: e.target.files[0], type: 'resume' })} 
                        />
                    </label>
                )
            )}
            {data.resumeUrl && (
                <a 
                    href={data.resumeUrl} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="theme-modern-btn theme-modern-btn-primary"
                >
                    <Download className="theme-modern-hero-action-icon" />
                    <span>Download Resume</span>
                </a>
            )}
        </div>
    </div>
);

export function ModernHero({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = useEditorSafe();
    const isExport = useIsExport();

    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;
    const isUploadingProfile = isEditing && context && context.state.isUploading?.type === 'profile';
    const isUploadingResume = isEditing && context && context.state.isUploading?.type === 'resume';

    // Clean existing HTML data on component mount
    useEffect(() => {
        if (isEditing && dispatch && data.userName) {
            const cleanUserName = stripHtml(data.userName);
            if (cleanUserName !== data.userName) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'userName', value: cleanUserName } });
            }
        }
        if (isEditing && dispatch && data.profession) {
            const cleanProfession = stripHtml(data.profession);
            if (cleanProfession !== data.profession) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'profession', value: cleanProfession } });
            }
        }
    }, [isEditing, dispatch, data.userName, data.profession]);

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    return (
        <section id="top" className="theme-modern-hero">
            <div className="theme-modern-hero-container">
                <div className="theme-modern-hero-layout">
                    <ProfileImageUploader 
                        isEditing={isEditing} 
                        data={data} 
                        isUploadingProfile={isUploadingProfile ?? false} 
                        onImageUpload={onImageUpload ?? (() => {})} 
                    />
                    <HeroContent 
                        isEditing={isEditing} 
                        data={data} 
                        handleUpdate={handleUpdate} 
                        isUploadingResume={isUploadingResume ?? false} 
                        onImageUpload={onImageUpload ?? (() => {})} 
                        isExport={isExport} 
                    />
                </div>
            </div>
        </section>
    );
};
