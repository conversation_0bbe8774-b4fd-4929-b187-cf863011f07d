"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Mail, Phone, Github, Linkedin, Twitter, MessageCircle, ExternalLink } from "lucide-react";
import { useAuthStore } from "@/stores/auth-store";

export function ModernContact({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const { user } = useAuthStore();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    // Use user's email as default if no email is set
    const defaultEmail = user?.email || '<EMAIL>';

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    const contactItems = [
        {
            icon: Mail,
            label: "Email",
            value: data.email || defaultEmail,
            field: 'email' as keyof Omit<PortfolioData, 'projects'>,
            href: `mailto:${data.email || defaultEmail}`,
            placeholder: "<EMAIL>"
        },
        {
            icon: Phone,
            label: "Phone",
            value: data.phone,
            field: 'phone' as keyof Omit<PortfolioData, 'projects'>,
            href: `tel:${data.phone}`,
            placeholder: "+****************"
        }
    ];

    const socialLinks = [
        {
            icon: Github,
            url: data.githubUrl,
            field: 'githubUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "GitHub",
            placeholder: "https://github.com/username"
        },
        {
            icon: Linkedin,
            url: data.linkedinUrl,
            field: 'linkedinUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "LinkedIn",
            placeholder: "https://linkedin.com/in/username"
        },
        {
            icon: Twitter,
            url: data.twitterUrl,
            field: 'twitterUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "Twitter",
            placeholder: "https://twitter.com/username"
        }
    ];

    return (
        <section id="contact" className="modern-contact">
            <div className="modern-contact-container">
                {/* Section Header */}
                <div className="modern-contact-header">
                    <div className="modern-contact-header-icon">
                        <MessageCircle size={32} />
                    </div>
                    <h2 className="modern-contact-title">Get In Touch</h2>
                    <div className="modern-contact-divider"></div>
                    <p className="modern-contact-subtitle">
                        Ready to collaborate? Let&#39;s discuss your next project and bring your ideas to life.
                    </p>
                </div>

                {/* Contact Information */}
                <div className="modern-contact-content">
                    <div className="modern-contact-info-grid">
                        {contactItems.map((item) => {
                            const IconComponent = item.icon;
                            // Always show email, only show phone if it has a value or is being edited
                            if (item.field === 'phone' && !item.value && !isEditing) return null;

                            return (
                                <div key={item.field} className="modern-contact-info-card">
                                    <div className="modern-contact-info-header">
                                        <div className="modern-contact-info-icon">
                                            <IconComponent size={20} />
                                        </div>
                                        <h3 className="modern-contact-info-label">{item.label}</h3>
                                    </div>
                                    <div className="modern-contact-info-content">
                                        <EditableText
                                            isEditing={isEditing}
                                            tagName="div"
                                            className={`modern-contact-info-text ${isEditing ? 'editable-field editable-field-modern' : ''}`}
                                            initialValue={item.value || item.placeholder}
                                            placeholder={item.placeholder}
                                            onSave={(value) => handleUpdate(item.field, value)}
                                        />
                                        {!isEditing && item.value && (
                                            <a
                                                href={item.href}
                                                className="modern-contact-info-link"
                                                target={item.field === 'email' ? '_self' : '_blank'}
                                                rel={item.field === 'email' ? '' : 'noopener noreferrer'}
                                            >
                                                <ExternalLink size={16} />
                                                Contact
                                            </a>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    {/* Social Links - always show in editing mode, or if there are actual social links */}
                    {(isEditing || socialLinks.some(link => link.url)) && (
                        <div className="modern-contact-social-section">
                            <h3 className="modern-contact-social-title">Connect With Me</h3>
                            <div className="modern-contact-social-grid">
                                {socialLinks.map((social) => {
                                    const IconComponent = social.icon;
                                    if (!social.url && !isEditing) return null;

                                    return (
                                        <div key={social.field} className="modern-contact-social-card">
                                            <div className="modern-contact-social-header">
                                                <div className="modern-contact-social-icon">
                                                    <IconComponent size={20} />
                                                </div>
                                                <h4 className="modern-contact-social-label">{social.label}</h4>
                                            </div>
                                            <div className="modern-contact-social-content">
                                                {isEditing && <EditableText
                                                    isEditing={isEditing}
                                                    tagName="div"
                                                    className={`modern-contact-social-url ${isEditing ? 'editable-field-inline editable-field-modern' : ''}`}
                                                    initialValue={social.url || social.placeholder}
                                                    placeholder={social.placeholder}
                                                    onSave={(value) => handleUpdate(social.field, value)}
                                                />}
                                                {!isEditing && social.url && (
                                                    <a
                                                        href={social.url}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="modern-contact-social-link"
                                                        aria-label={social.label}
                                                    >
                                                        <ExternalLink size={16} />
                                                        Visit
                                                    </a>
                                                )}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
