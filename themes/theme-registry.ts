import React from 'react';

// Import theme components
import { ModernTheme } from './modern/components/ModernTheme';
import { CreativeMinimalistTheme } from './creative-minimalist/components/CreativeMinimalistTheme';
import { ProfolifyThemeProps } from '@/lib/types';

export interface ThemeConfig {
  id: string;
  name: string;
  description: string;
  cssFile: string; // Public CSS file path for client-side loading
  sourceCssFile: string; // Source CSS file path in themes directory
  component: React.ComponentType<ProfolifyThemeProps>;
  preview?: string;
  category: 'modern' | 'minimalist' | 'creative' | 'professional';
  version: string;
  author?: string;
}

export const THEME_REGISTRY: ThemeConfig[] = [
  {
    id: 'modern-theme-v1',
    name: 'Modern',
    description: 'A sleek, modern design with gradient backgrounds and smooth animations',
    cssFile: '/themes/modern/modern-compiled.css',
    sourceCssFile: 'themes/modern/modern-compiled.css',
    component: ModernTheme,
    category: 'modern',
    version: '1.0.0',
    author: 'Profolify Team',
    preview: '/thumbnails/Modern.png', // <-- Add this line

  },
  {
    id: 'creative-theme-v1',
    name: 'Creative Minimalist',
    description: 'Clean, minimalist design focused on content and readability',
    cssFile: '/themes/creative-minimalist/creative-minimalist-compiled.css',
    sourceCssFile: 'themes/creative-minimalist/creative-minimalist-compiled.css',
    component: CreativeMinimalistTheme,
    category: 'minimalist',
    version: '1.0.0',
    author: 'Profolify Team',
    preview: '/thumbnails/Creative Minimalist.png', // <-- Add this line

  },
];

/**
 * Get theme configuration by ID
 */
export function getThemeById(themeId: string): ThemeConfig | undefined {
  return THEME_REGISTRY.find(theme => theme.id === themeId);
}

/**
 * Get theme component by ID
 */
export function getThemeComponent(themeId: string): React.ComponentType<ProfolifyThemeProps> | undefined {
  const theme = getThemeById(themeId);
  return theme?.component;
}

/**
 * Get theme CSS file path by ID
 */
export function getThemeCssPath(themeId: string): string | undefined {
  const theme = getThemeById(themeId);
  return theme?.cssFile;
}

/**
 * Get all available themes
 */
export function getAllThemes(): ThemeConfig[] {
  return THEME_REGISTRY;
}

/**
 * Get themes by category
 */
export function getThemesByCategory(category: ThemeConfig['category']): ThemeConfig[] {
  return THEME_REGISTRY.filter(theme => theme.category === category);
}

/**
 * Check if theme exists
 */
export function themeExists(themeId: string): boolean {
  return THEME_REGISTRY.some(theme => theme.id === themeId);
}

/**
 * Get default theme (fallback)
 */
export function getDefaultTheme(): ThemeConfig {
  return THEME_REGISTRY[0]; // Modern theme as default
}

/**
 * Load theme CSS content from file system (server-side only)
 * This function is only available on the server-side and will not be bundled for the client
 */
export async function loadThemeCss(): Promise<string | null> {
  // This function should only be called on the server-side
  // For client-side usage, use getThemeCssUrl() and fetch the CSS directly
  return null;
}

/**
 * Get theme CSS URL for client-side loading
 */
export function getThemeCssUrl(themeId: string): string | null {
  const theme = getThemeById(themeId);
  return theme?.cssFile || null;
}

/**
 * Get theme source CSS file path
 */
export function getThemeSourceCssPath(themeId: string): string | null {
  const theme = getThemeById(themeId);
  return theme?.sourceCssFile || null;
}

/**
 * Validate theme configuration
 */
export function validateThemeConfig(config: ThemeConfig): string[] {
  const errors: string[] = [];

  if (!config.id) errors.push('Theme ID is required');
  if (!config.name) errors.push('Theme name is required');
  if (!config.cssFile) errors.push('CSS file path is required');
  if (!config.sourceCssFile) errors.push('Source CSS file path is required');
  if (!config.component) errors.push('Theme component is required');
  if (!config.version) errors.push('Theme version is required');

  // Check for duplicate IDs
  const existingTheme = getThemeById(config.id);
  if (existingTheme) errors.push(`Theme ID '${config.id}' already exists`);

  return errors;
}
