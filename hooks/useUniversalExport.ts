import { useState } from 'react';
import { PortfolioData } from '@/lib/types';
import J<PERSON>Z<PERSON> from 'jszip';

/**
 * Universal Export Hook
 * 
 * This hook provides a theme-agnostic export system that works with any theme
 * without requiring hardcoded HTML generation.
 */
export function useUniversalExport() {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState<string>('');

  /**
   * Export portfolio using data-driven approach (works from dashboard)
   */
  const exportWithDataDrivenApproach = async (portfolioData: PortfolioData): Promise<boolean> => {
    try {
      setExportProgress('Detecting current page context...');

      // Check if we're on the portfolio editor page (has theme rendered)
      const themeRoot = document.querySelector(`[class*="theme-"][class*="-root"]`);

      console.log('🔍 Theme root detection:', {
        found: !!themeRoot,
        currentPath: window.location.pathname,
        templateId: portfolioData.templateId
      });

      if (themeRoot) {
        console.log('✅ Found theme root - using DOM capture method');
        setExportProgress('Using live theme capture...');
        return await exportFromRenderedTheme(portfolioData, themeRoot);
      } else {
        console.log('📄 No theme root found - using data-driven fallback');
        setExportProgress('Using data-driven export...');
        return await exportFromPortfolioData(portfolioData);
      }
    } catch (error) {
      console.error('Data-driven export failed:', error);
      throw error;
    }
  };

  /**
   * Export from rendered theme (when on editor page)
   */
  const exportFromRenderedTheme = async (portfolioData: PortfolioData, themeRoot: Element): Promise<boolean> => {
    setExportProgress('Capturing rendered theme...');

    // Get theme CSS
    const { getThemeCssUrl } = await import('@/themes/theme-registry');
    const cssUrl = getThemeCssUrl(portfolioData.templateId);

    if (!cssUrl) {
      throw new Error(`No CSS URL found for theme: ${portfolioData.templateId}`);
    }

    const response = await fetch(cssUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch CSS: ${response.status}`);
    }
    const cssContent = await response.text();

    setExportProgress('Processing HTML structure...');

    // Clone and clean the theme HTML
    const clonedHTML = themeRoot.cloneNode(true) as HTMLElement;
    cleanHTMLForExport(clonedHTML);

    // Generate complete HTML
    const completeHTML = generateCompleteHTML(portfolioData, clonedHTML.outerHTML, cssContent);

    setExportProgress('Creating download package...');

    // Create ZIP
    const zip = new JSZip();
    zip.file('index.html', completeHTML);
    zip.file('README.md', generateReadme(portfolioData));

    const zipBlob = await zip.generateAsync({ type: 'blob' });
    downloadZip(zipBlob, portfolioData.slug || 'portfolio');

    return true;
  };

  /**
   * Export from portfolio data using live DOM capture
   */
  const exportFromPortfolioData = async (portfolioData: PortfolioData): Promise<boolean> => {
    try {
      setExportProgress('Using live DOM capture...');

      // Use the new live DOM capture system
      const { exportWithLiveDOMCapture } = await import('@/lib/live-dom-capture');

      console.log('✅ Using live DOM capture export');
      setExportProgress('Capturing live portfolio...');

      await exportWithLiveDOMCapture(portfolioData);
      return true;

    } catch (error) {
      console.error('❌ Live DOM capture failed, falling back to original:', error);

      // Fallback to original system
      setExportProgress('Using fallback export method...');
      const { exportPortfolioAsZip } = await import('@/lib/client-export');

      setExportProgress('Generating theme HTML...');
      await exportPortfolioAsZip(portfolioData);

      return true;
    }
  };



  /**
   * Main export function with smart detection
   */
  const exportPortfolio = async (portfolioData: PortfolioData): Promise<boolean> => {
    setIsExporting(true);
    setExportProgress('Starting export...');

    try {
      // Use data-driven approach (works from dashboard and editor)
      await exportWithDataDrivenApproach(portfolioData);
      setExportProgress('Export completed successfully!');
      return true;
    } catch (error) {
      setExportProgress('Export failed');
      console.error('Export failed:', error);
      throw new Error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExporting(false);
      setTimeout(() => setExportProgress(''), 3000);
    }
  };

  return {
    exportPortfolio,
    isExporting,
    exportProgress,
  };
}

/**
 * Clean HTML for static export
 */
function cleanHTMLForExport(element: HTMLElement): void {
  // Remove editing attributes
  const editingElements = element.querySelectorAll('[contenteditable], [data-editing]');
  editingElements.forEach(el => {
    el.removeAttribute('contenteditable');
    el.removeAttribute('data-editing');
    if (el.classList.contains('editing')) {
      el.classList.remove('editing');
    }
  });

  // Remove loading states
  const loadingElements = element.querySelectorAll('.loading, .spinner, [data-loading]');
  loadingElements.forEach(el => el.remove());

  // Remove edit buttons and controls
  const editControls = element.querySelectorAll('[data-edit], .edit-btn, .add-btn, .delete-btn');
  editControls.forEach(el => el.remove());
}

/**
 * Generate complete HTML document
 */
function generateCompleteHTML(portfolioData: PortfolioData, bodyHTML: string, cssContent: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${portfolioData.userName || 'Portfolio'}</title>
  <meta name="description" content="${portfolioData.about || 'Professional portfolio'}">
  <style>
    ${cssContent}
    
    /* Export optimizations */
    * { -webkit-print-color-adjust: exact !important; }
    html { scroll-behavior: smooth; }
    img { max-width: 100%; height: auto; }
    .loading, .spinner { display: none !important; }
  </style>
</head>
<body>
  ${bodyHTML}
  
  <script>
    // Smooth scroll navigation
    document.addEventListener('DOMContentLoaded', function() {
      const links = document.querySelectorAll('a[href^="#"]');
      links.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) target.scrollIntoView({ behavior: 'smooth' });
        });
      });
    });
  </script>
</body>
</html>`;
}

/**
 * Generate README file
 */
function generateReadme(portfolioData: PortfolioData): string {
  return `# ${portfolioData.userName || 'Portfolio'} - Static Export

This portfolio was exported from Profolify and is ready to host!

## Files:
- **index.html** - Your complete portfolio website (ready to upload!)
- **README.md** - This file

## How to Use:
1. Upload index.html to any web hosting service
2. That's it! Your portfolio is live

## Hosting Options:
- **Netlify**: Drag & drop index.html
- **Vercel**: Upload the file
- **GitHub Pages**: Commit to repository
- **Any Web Host**: Upload via FTP/control panel

## Details:
- Theme: ${portfolioData.templateId}
- Generated: ${new Date().toISOString()}
- Exported from: https://profolify.com

## Need Changes?
Visit https://profolify.com to edit and re-export your portfolio anytime!
`;
}

/**
 * Download ZIP file
 */
function downloadZip(zipBlob: Blob, filename: string): void {
  const url = URL.createObjectURL(zipBlob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.zip`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
