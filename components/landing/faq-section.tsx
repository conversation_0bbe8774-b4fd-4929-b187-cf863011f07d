"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { HelpCircle, Mail, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const faqs = [
  {
    question: "Is this portfolio builder really free?",
    answer:
      "Yes! Our portfolio builder is completely free forever. Create professional portfolios, export static HTML files, and publish online without any hidden costs or time limits. We believe everyone should have access to professional portfolio creation tools.",
  },
  {
    question: "How do I export my portfolio as static HTML files?",
    answer:
      "Simply click the 'Export' button in your portfolio editor to download a complete ZIP file containing your portfolio as static HTML, CSS, and JavaScript files. These files work on any web hosting platform and can be uploaded anywhere for instant deployment.",
  },
  {
    question: "What makes this different from other portfolio builders?",
    answer:
      "Our portfolio builder features revolutionary Live DOM Capture technology that ensures your exported HTML files match your live portfolio pixel-for-pixel. Plus, we offer a true WYSIWYG editor, SEO-optimized themes, and mobile-responsive designs - all completely free.",
  },
  {
    question: "Do I need coding skills to build a professional portfolio?",
    answer:
      "No coding required! Our portfolio builder features an intuitive WYSIWYG editor. Simply click on any text to edit it in place, upload images, and customize your portfolio without writing a single line of code.",
  },
  {
    question: "Are the portfolio themes mobile-responsive?",
    answer:
      "Yes! All our portfolio themes are fully mobile-responsive and optimized for all devices including smartphones, tablets, and desktops. Your professional portfolio will look perfect on any screen size.",
  },
  {
    question: "Can I use this for my resume and CV portfolio?",
    answer:
      "Absolutely! Our portfolio builder is perfect for creating resume portfolios and CV websites. Showcase your work experience, skills, projects, and achievements in a professional format that employers love.",
  },
  {
    question: "How are the portfolios optimized for SEO?",
    answer:
      "All portfolios are automatically SEO-optimized with proper meta tags, structured data, clean URLs, and mobile-responsive design. This helps your portfolio rank better in search engines and get discovered by potential employers or clients.",
  },
  {
    question: "Can I create portfolios for different professions?",
    answer:
      "Yes! Our portfolio builder works for developers, designers, freelancers, photographers, writers, marketers, and any professional who wants to showcase their work online. Choose from themes designed for different industries.",
  },
  {
    question: "What's included in the static HTML export?",
    answer:
      "Your HTML export includes a complete, self-contained website with embedded CSS, optimized images, mobile-responsive design, and all necessary files to host anywhere. No external dependencies required.",
  },
  {
    question: "How quickly can I build and publish a portfolio?",
    answer:
      "You can create and publish a professional portfolio in minutes! Our intuitive editor and pre-designed themes make it incredibly fast to build stunning portfolios without any technical knowledge.",
  },
];

export function FAQSection() {
  return (
    <section className="py-24 lg:py-32 bg-backgroundPrimary relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundSecondary via-backgroundPrimary to-backgroundSecondary"></div>
      <div className="absolute top-20 left-10 w-64 h-64 bg-gradient-to-r from-brandPrimary/4 to-brandSecondary/4 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-brandAccent/3 to-brandPrimary/3 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl glass-effect border border-brandPrimary/30 backdrop-blur-2xl mb-8">
            <HelpCircle className="w-5 h-5 text-brandPrimary" />
            <span className="text-sm font-semibold text-brandPrimary">
              Frequently Asked Questions
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="text-textPrimary">Got</span>
            <span className="gradient-text"> Questions?</span>
            <br />
            <span className="text-textPrimary">We&#39;ve Got Answers</span>
          </h2>

          <p className="text-xl md:text-2xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Everything you need to know about Profolify, from getting started to
            advanced features.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* FAQ Accordion */}
          <div className="glass-effect rounded-3xl p-8 lg:p-12 border border-borderPrimary/50 backdrop-blur-2xl mb-12">
            <Accordion type="single" collapsible className="space-y-6">
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border border-borderPrimary/30 rounded-2xl px-6 py-2 hover:border-brandPrimary/40 transition-colors duration-300"
                >
                  <AccordionTrigger className="text-left text-lg font-semibold text-textPrimary hover:text-brandPrimary transition-colors py-6 [&[data-state=open]]:text-brandPrimary">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-textSecondary leading-relaxed pb-6 pt-2">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          {/* Contact Support CTA */}
          <div className="text-center">
            <div className="glass-effect rounded-3xl p-8 lg:p-10 border border-borderPrimary/50 backdrop-blur-2xl">
              <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center shadow-lg shadow-brandPrimary/25">
                  <MessageCircle className="w-8 h-8 text-white" />
                </div>
              </div>

              <h3 className="text-2xl font-bold text-textPrimary mb-4">
                Still have questions?
              </h3>

              <p className="text-textSecondary mb-6 max-w-2xl mx-auto">
                Can&#39;t find the answer you&#39;re looking for? Our friendly support
                team is here to help you get the most out of Profolify.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                {/* <Button
                  variant="outline"
                  className="px-8 py-3 rounded-2xl border-2 border-borderPrimary/50 hover:border-brandPrimary/50 glass-effect backdrop-blur-2xl"
                >
                  Browse Documentation
                </Button> */}


                <Link
                  href="mailto:<EMAIL>"
                  className="w-8 h-8 rounded-lg bg-backgroundPrimary/50 border border-borderPrimary/50 flex items-center justify-center text-textSecondary hover:text-brandPrimary hover:border-brandPrimary/50 transition-colors duration-200"
                  aria-label="Email"
                >
                  <Button className="bg-gradient-to-r from-brandPrimary to-brandSecondary text-white px-8 py-3 rounded-2xl border-0 shadow-lg shadow-brandPrimary/25">
                    Contact Support  <Mail className="w-4 h-4" />
                  </Button>

                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
