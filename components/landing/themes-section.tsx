"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Download, Pa<PERSON>, <PERSON>rk<PERSON>, Check } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";

const themes = [
  {
    id: "modern-theme-v1",
    name: "Modern",
    description: "Clean, professional design with dark mode styling. Perfect for developers, designers, and creative professionals who prefer darker interfaces.",
    image: "/thumbnails/Modern.png",
    category: "Dark Theme",
    features: ["Dark Mode", "Responsive Design", "SEO Optimized"],
    color: "from-slate-600 to-gray-800"
  },
  {
    id: "creative-theme-v1",
    name: "Creative Minimalist",
    description: "Minimalist design with light, clean layouts. Ideal for professionals who prefer bright, airy interfaces with excellent readability.",
    image: "/thumbnails/Creative Minimalist.png",
    category: "Light Theme",
    features: ["Light Mode", "Responsive Design", "SEO Optimized"],
    color: "from-blue-400 to-cyan-500"
  }
];

export function ThemesSection() {
  const router = useRouter();

  return (
    <section
      id="themes"
      className="py-24 lg:py-32 bg-backgroundSecondary relative overflow-hidden"
    >
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-brandPrimary/6 to-brandSecondary/6 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-brandAccent/4 to-brandPrimary/4 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-brandSecondary/3 to-brandAccent/3 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Enhanced Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl bg-gradient-to-r from-brandSecondary/10 to-brandPrimary/10 border border-brandSecondary/30 backdrop-blur-sm mb-8">
            <Palette className="w-5 h-5 text-brandSecondary" />
            <span className="text-sm font-bold text-brandSecondary">
              Professional Portfolio Themes
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">Choose Your Perfect</span>
            <br />
            <span className="text-textPrimary">Portfolio Style</span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Start with our carefully crafted, mobile-responsive themes. Each design is
            professionally optimized for SEO and ready to showcase your work beautifully.
          </p>
        </div>

        {/* Enhanced Themes Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 max-w-7xl mx-auto mb-20">
          {themes.map((theme) => (
            <div
              key={theme.id}
              className="group relative"
            >
              {/* Main Card */}
              <div className="relative bg-white/5 backdrop-blur-xl rounded-3xl border border-white/10 overflow-hidden hover:border-white/20 transition-all duration-500 hover:shadow-2xl hover:shadow-brandPrimary/10 group-hover:scale-[1.02]">

                {/* Theme Preview */}
                <div className="relative overflow-hidden">
                  <div className={`absolute inset-0 bg-gradient-to-br ${theme.color} opacity-10 group-hover:opacity-20 transition-opacity duration-500`}></div>
                  <Image
                    src={theme.image}
                    alt={`${theme.name} theme preview`}
                    width={600}
                    height={400}
                    className="w-full h-64 sm:h-72 lg:h-80 object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>

                {/* Theme Content */}
                <div className="p-6 lg:p-8">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl lg:text-2xl font-bold text-textPrimary mb-2 group-hover:text-brandPrimary transition-colors duration-300">
                        {theme.name}
                      </h3>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r ${theme.color} text-white`}>
                        {theme.category}
                      </span>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-textSecondary text-sm lg:text-base leading-relaxed mb-6">
                    {theme.description}
                  </p>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-textPrimary mb-3">Features:</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {theme.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2">
                          <Check className="w-3 h-3 text-green-500 flex-shrink-0" />
                          <span className="text-xs text-textSecondary">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-center">
                    <Button
                      onClick={() => router.push("/login")}
                      variant="default"
                      size="lg"
                      className="w-full group-hover:shadow-xl group-hover:shadow-brandPrimary/25"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Use This Theme
                    </Button>
                  </div>
                </div>

                {/* Animated Border */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${theme.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 pointer-events-none`}></div>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced CTA Section */}
        <div className="relative">
          {/* Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-brandPrimary/10 via-brandSecondary/10 to-brandAccent/10 rounded-3xl blur-xl"></div>

          <div className="relative bg-white/5 backdrop-blur-xl rounded-3xl border border-white/10 p-8 lg:p-12 text-center">
            <div className="mb-6">
              <Sparkles className="w-12 h-12 text-brandPrimary mx-auto mb-4" />
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="gradient-text">More Themes Coming Soon</span>
              </h3>
            </div>

            <p className="text-textSecondary text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
              We&apos;re crafting additional stunning themes including Business Professional,
              Portfolio Showcase, Creative Agency, and Developer-focused designs.
              <span className="text-brandPrimary font-semibold">Join now to get early access!</span>
            </p>

            <div className="flex justify-center">
              <Button
                onClick={() => router.push("/login")}
                variant="cta"
                size="xl"
                className="group"
              >
                <span className="flex items-center gap-2">
                  Start Building Your Portfolio
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
              </Button>
            </div>

            {/* Stats */}
            <div className="mt-8 pt-8 border-t border-white/10">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-2xl font-bold text-brandPrimary mb-1">2</div>
                  <div className="text-sm text-textSecondary">Professional Themes</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-brandSecondary mb-1">100%</div>
                  <div className="text-sm text-textSecondary">Mobile Responsive</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-brandAccent mb-1">Free</div>
                  <div className="text-sm text-textSecondary">Forever</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
