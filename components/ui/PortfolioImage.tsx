import { useIsExport } from '@/contexts/ExportContext';
import NextImage from 'next/image';

interface PortfolioImageProps {
    src: string;
    alt: string;
    className?: string;
    width?: number;
    height?: number;
    priority?: boolean;
    fill?: boolean;
    isEditing?: boolean;
}

export function PortfolioImage(props: PortfolioImageProps) {
    const isExport = useIsExport();

    // Destructure out isEditing so it's not passed to the underlying img element.
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { isEditing, ...rest } = props;

    // Check if we're in an export context or if the URL suggests we should use regular img
    const shouldUseRegularImg = isExport ||
        (typeof window !== 'undefined' && window.location.pathname.includes('/export')) ||
        (typeof document !== 'undefined' && document.documentElement.hasAttribute('data-export')) ||
        (typeof window !== 'undefined' && (window as typeof window & { __PORTFOLIO_EXPORT__?: boolean }).__PORTFOLIO_EXPORT__);

    // If we are exporting to static HTML, render a standard <img> tag.
    // This uses the direct Cloudinary/placeholder URL and will work anywhere.
    if (shouldUseRegularImg) {
        console.log('🖼️ PortfolioImage: Using regular img tag for export, src:', props.src);

        // We filter out Next.js specific props that are not valid for a standard img tag.
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { fill, priority, ...imgRest } = rest;

        // Ensure we have a valid src for export
        let exportSrc = props.src;

        // Handle Next.js optimized URLs by extracting the original URL
        if (exportSrc && exportSrc.includes('/_next/image')) {
            try {
                const urlParams = new URLSearchParams(exportSrc.split('?')[1]);
                const originalUrl = urlParams.get('url');
                if (originalUrl) {
                    exportSrc = decodeURIComponent(originalUrl);
                    console.log('🖼️ PortfolioImage: Extracted original URL from Next.js optimization:', exportSrc);
                } else {
                    exportSrc = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
                    console.log('🖼️ PortfolioImage: Could not extract URL, using placeholder');
                }
            } catch (error) {
                console.warn('🖼️ PortfolioImage: Failed to parse Next.js URL:', error);
                exportSrc = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
            }
        }

        // Handle blob URLs (shouldn't happen in export but just in case)
        if (exportSrc && exportSrc.startsWith('blob:')) {
            exportSrc = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
            console.log('🖼️ PortfolioImage: Replaced blob URL with placeholder');
        }

        // Handle relative URLs by making them absolute
        if (exportSrc && exportSrc.startsWith('/') && !exportSrc.startsWith('//')) {
            exportSrc = window.location.origin + exportSrc;
            console.log('🖼️ PortfolioImage: Made relative URL absolute:', exportSrc);
        }

        console.log('🖼️ PortfolioImage: Final export src:', exportSrc);

        return (
            // eslint-disable-next-line @next/next/no-img-element
            <img
                {...imgRest}
                src={exportSrc}
                alt={props.alt}
                loading="lazy"
                onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    console.log('🖼️ PortfolioImage: Image failed to load, using placeholder:', target.src);
                    target.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
                }}
            />
        );
    }

    console.log('🖼️ PortfolioImage: Using Next.js Image component for live site, src:', props.src);
    // Otherwise (on your live Next.js site), render the optimized <Image /> component.
    return <NextImage {...rest} />;
}