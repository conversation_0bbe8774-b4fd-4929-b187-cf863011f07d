"use client";
import { Check, Loader2, AlertCircle } from "lucide-react";

type SaveStatus = 'idle' | 'saving' | 'saved' | 'error';

export function SaveStatusIndicator({ status }: { status: SaveStatus }) {
    if (status === 'saving') {
        return (
            <div className="flex items-center text-sm text-muted-foreground">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
            </div>
        );
    }
    if (status === 'saved') {
        return (
            <div className="flex items-center text-sm text-green-600">
                <Check className="mr-2 h-4 w-4" /> All changes saved
            </div>
        );
    }
    if (status === 'error') {
        return (
            <div className="flex items-center text-sm text-red-600">
                <AlertCircle className="mr-2 h-4 w-4" /> Save failed
            </div>
        );
    }
    return null;
}