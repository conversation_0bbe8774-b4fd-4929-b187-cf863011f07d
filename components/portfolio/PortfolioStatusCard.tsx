"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  ExternalLink,
  Eye,
  Download,
  Copy,
  Share2,
  Loader2,
  Check,
  Twitter,
  Facebook,
  Linkedin,
  Mail,
} from "lucide-react"
import { toast } from "sonner"

interface PortfolioStatusCardProps {
  portfolio: {
    isPublished: boolean
    slug: string
  }
  onPreview?: () => void
  onExport?: () => void
  isExporting?: boolean
}

export function PortfolioStatusCard({ 
  portfolio, 
  onPreview, 
  onExport, 
  isExporting = false 
}: PortfolioStatusCardProps) {
  const [copied, setCopied] = useState(false)

  const fullUrl = `${typeof window !== "undefined" ? window.location.origin : "https://www.profolify.com"}/${portfolio.slug}`
  const shortUrl = `profolify.com/${portfolio.slug}`

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(fullUrl)
      setCopied(true)
      toast.success("Link copied to clipboard!")
      setTimeout(() => setCopied(false), 2000)
    } catch {
      toast.error("Failed to copy link. Please try again.",)
    }
  }

  const handleShare = (platform: string) => {
    const text = "Check out my portfolio!"
    const encodedUrl = encodeURIComponent(fullUrl)
    const encodedText = encodeURIComponent(text)

    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      email: `mailto:?subject=${encodedText}&body=${encodedText}%20${encodedUrl}`,
    }

    if (shareUrls[platform as keyof typeof shareUrls]) {
      window.open(shareUrls[platform as keyof typeof shareUrls], "_blank", "width=600,height=400")
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="text-xl font-semibold tracking-tight">Portfolio Status</h3>
            <p className="text-sm text-muted-foreground">
              Your portfolio is currently {portfolio.isPublished ? "published and live" : "a draft"}.
            </p>
          </div>
          <Badge
            variant={portfolio.isPublished ? "default" : "secondary"}
            className={`${
              portfolio.isPublished
                ? "bg-green-100 text-green-800 border-green-200 hover:bg-green-100"
                : "bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-100"
            }`}
          >
            <div className={`w-2 h-2 rounded-full mr-2 ${portfolio.isPublished ? "bg-green-500" : "bg-yellow-500"}`} />
            {portfolio.isPublished ? "Live" : "Draft"}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* URL Section */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">Live URL</label>
          <div className="flex items-center gap-2">
            <div className="flex-1 relative">
              <Input readOnly value={shortUrl} className="pr-20 font-mono text-sm" />
              <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                <Button size="sm" variant="ghost" onClick={handleCopyLink} className="h-7 px-2">
                  {copied ? <Check className="h-3 w-3 text-green-600" /> : <Copy className="h-3 w-3" />}
                </Button>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="px-3 bg-transparent">
                  <Share2 className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleShare("twitter")}>
                  <Twitter className="h-4 w-4 mr-2 hover:text-white" />
                  Share on Twitter
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("facebook")}>
                  <Facebook className="h-4 w-4 mr-2 hover:text-white" />
                  Share on Facebook
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleShare("linkedin")}>
                  <Linkedin className="h-4 w-4 mr-2 hover:text-white" />
                  Share on LinkedIn
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleShare("email")}>
                  <Mail className="h-4 w-4 mr-2 hover:text-white" />
                  Share via Email
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          {portfolio.isPublished ? (
            <Button asChild className="bg-blue-600 hover:bg-blue-700">
              <Link href={`/${portfolio.slug}`} target="_blank">
                <ExternalLink className="mr-2 h-4 w-4" />
                View Live
              </Link>
            </Button>
          ) : (
            <Button onClick={onPreview} variant="outline">
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </Button>
          )}

          <Button asChild variant="secondary">
            <Link href="/portfolio">Edit Portfolio</Link>
          </Button>

          <Button 
            onClick={onExport} 
            className="bg-green-600 hover:bg-green-700 text-white" 
            disabled={isExporting}
          >
            {isExporting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="mr-2 h-4 w-4" />}
            {isExporting ? "Exporting..." : "Export Site"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
