"use client";
import { createContext, useContext, useReducer, Dispatch } from 'react';
import { PortfolioData, Project } from '@/lib/types';

// State and Actions
type EditorState = {
    formData: PortfolioData;
    isUploading: { type: string; id?: string } | null;
    saveStatus: 'idle' | 'saving' | 'saved' | 'error';
};

export type Action =
    | { type: 'INITIALIZE_DATA'; payload: PortfolioData }
    | { type: 'UPDATE_FIELD'; payload: { field: keyof Omit<PortfolioData, 'projects'>; value: unknown } }
    | { type: 'ADD_PROJECT' }
    | { type: 'UPDATE_PROJECT'; payload: { index: number; field: keyof Project; value: unknown } }
    | { type: 'DELETE_PROJECT'; payload: { id: string } }
    | { type: 'SET_UPLOADING'; payload: { type: string; id?: string } | null }
    | { type: 'SET_SAVE_STATUS'; payload: EditorState['saveStatus'] }
    // --- NEW: A dedicated action to sync state after a successful save ---
    | { type: 'SYNC_SAVED_DATA'; payload: PortfolioData };
// Reducer Function
function editorReducer(state: EditorState, action: Action): EditorState {
    switch (action.type) {
        case 'INITIALIZE_DATA':
            return { ...state, formData: action.payload };
        // It simply replaces the form data with the new, authoritative state from the server.
        case 'SYNC_SAVED_DATA':
            return { ...state, formData: action.payload };

        case 'UPDATE_FIELD':
            return { ...state, formData: { ...state.formData, [action.payload.field]: action.payload.value } };
        case 'ADD_PROJECT':
            // Generate a more unique ID to avoid conflicts
            const timestamp = Date.now();
            const randomSuffix = Math.random().toString(36).substring(2, 11);
            const newProject: Project = {
                id: `project-${timestamp}-${randomSuffix}`,
                title: 'New Project',
                description: 'A brief description of your awesome project.',
            };
            return { ...state, formData: { ...state.formData, projects: [...state.formData.projects, newProject] } };
        case 'UPDATE_PROJECT':
            const updatedProjects = [...state.formData.projects];
            updatedProjects[action.payload.index] = { ...updatedProjects[action.payload.index], [action.payload.field]: action.payload.value };
            return { ...state, formData: { ...state.formData, projects: updatedProjects } };
        case 'DELETE_PROJECT':
            return { ...state, formData: { ...state.formData, projects: state.formData.projects.filter(p => p.id !== action.payload.id) } };
        case 'SET_UPLOADING':
            return { ...state, isUploading: action.payload };
        case 'SET_SAVE_STATUS':
            return { ...state, saveStatus: action.payload };
        default:
            throw new Error(`Unhandled action type`);
    }
}

// Context
interface EditorContextType {
    state: EditorState;
    dispatch: Dispatch<Action>;
}
const EditorContext = createContext<EditorContextType | undefined>(undefined);

// Provider
export function EditorProvider({ children, initialData }: { children: React.ReactNode, initialData: PortfolioData }) {
    const [state, dispatch] = useReducer(editorReducer, {
        formData: initialData,
        isUploading: null,
        saveStatus: 'idle',
    });
    return <EditorContext.Provider value={{ state, dispatch }}>{children}</EditorContext.Provider>;
}

// Hook
export function useEditor() {
    const context = useContext(EditorContext);
    if (context === undefined) {
        throw new Error('useEditor must be used within an EditorProvider');
    }
    return context;
}

// Safe hook that can be called even when not in editing mode
export function useEditorSafe() {
    const context = useContext(EditorContext);
    return context; // Returns undefined if not in EditorProvider
}