# 🔧 API Reference

This document provides comprehensive documentation for Portfolio Builder's API endpoints, database operations, and external service integrations.

## 🗂️ API Structure

Portfolio Builder uses Next.js API Routes for server-side operations and client-side SDKs for direct service communication.

### API Routes (`/app/api/`)
- **Theme Management**: CSS compilation and synchronization
- **Middleware**: Authentication and route protection
- **Utilities**: Helper endpoints for various operations

### Client-side APIs
- **Firebase SDK**: Direct Firestore and Auth operations
- **Cloudinary SDK**: Image upload and optimization
- **Export System**: Client-side portfolio export

## 🔥 Firebase API Operations

### Portfolio Management (`lib/portfolio-api.ts`)

#### Get Portfolio
```typescript
export const getPortfolio = async (userId: string): Promise<PortfolioData | null>
```
- **Purpose**: Retrieve user's portfolio data
- **Parameters**: `userId` - Firebase Auth UID
- **Returns**: Portfolio data or null if not found
- **Usage**: Dashboard and editor data loading

#### Create Portfolio from Template
```typescript
export const createPortfolioFromTemplate = async ({
  userId: string,
  userEmail: string | null,
  templateId: string
}): Promise<PortfolioData>
```
- **Purpose**: Create new portfolio with default data
- **Parameters**: 
  - `userId` - Firebase Auth UID
  - `userEmail` - User's email for pre-filling
  - `templateId` - Selected theme identifier
- **Returns**: New portfolio data with sample content
- **Default Data**: Includes sample experience, skill, and project

#### Update Portfolio
```typescript
export const updatePortfolio = async ({
  userId: string,
  data: Partial<PortfolioData>
}): Promise<void>
```
- **Purpose**: Update portfolio data
- **Parameters**: 
  - `userId` - Firebase Auth UID
  - `data` - Partial portfolio data to update
- **Usage**: Save changes from editor

#### Delete Portfolio
```typescript
export const deletePortfolio = async (userId: string): Promise<void>
```
- **Purpose**: Delete user's portfolio
- **Parameters**: `userId` - Firebase Auth UID
- **Usage**: Account deletion or portfolio reset

#### Get Portfolio by Slug
```typescript
export const getPortfolioBySlug = async (slug: string): Promise<PortfolioData | null>
```
- **Purpose**: Retrieve published portfolio by public URL
- **Parameters**: `slug` - Public portfolio URL slug
- **Returns**: Published portfolio data or null
- **Usage**: Public portfolio pages

#### Upload File
```typescript
export const uploadFile = async (file: File): Promise<string>
```
- **Purpose**: Upload files to Cloudinary
- **Parameters**: `file` - File object to upload
- **Returns**: Optimized file URL
- **Features**: Auto-optimization for images

## 🎨 Theme API

### Theme Registry (`themes/theme-registry.ts`)

#### Get Theme Component
```typescript
export const getThemeComponent = (templateId: string): React.ComponentType<ProfolifyThemeProps> | null
```
- **Purpose**: Get React component for theme
- **Parameters**: `templateId` - Theme identifier
- **Returns**: Theme component or null
- **Usage**: Dynamic theme rendering

#### Get Available Themes
```typescript
export const getAvailableThemes = (): ThemeInfo[]
```
- **Purpose**: List all available themes
- **Returns**: Array of theme information
- **Usage**: Template selection interface

### Theme Compilation API (`/api/sync-themes`)

#### POST /api/sync-themes
```typescript
// Endpoint: POST /api/sync-themes
// Purpose: Compile all theme CSS files
// Response: { success: boolean, message: string }
```
- **Purpose**: Compile and sync theme CSS files
- **Process**: 
  1. Detect all themes with modular structure
  2. Compile CSS files for each theme
  3. Copy compiled CSS to public directory
- **Usage**: Build process and development

## 📊 Data Models

### PortfolioData Interface
```typescript
interface PortfolioData {
  uid: string;                    // Firebase Auth UID
  isPublished: boolean;           // Publication status
  slug: string;                   // Public URL slug
  templateId: string;             // Selected theme
  userName: string;               // User's display name
  profession: string;             // Professional title
  about?: string;                 // About section content
  bio?: string;                   // Bio field for themes
  qualifications?: string;        // Qualifications info
  profileImageUrl?: string;       // Profile image URL
  resumeUrl?: string;            // Resume file URL
  projects: Project[];           // Project portfolio
  experiences: Experience[];     // Work experience
  skills: Skill[];              // Skills list
  socials: SocialLinks;         // Social media links
  contactEmail: string;         // Primary contact email
  email?: string;               // Contact form email
  phone?: string;               // Phone number
  githubUrl?: string;           // GitHub profile
  linkedinUrl?: string;         // LinkedIn profile
  twitterUrl?: string;          // Twitter profile
}
```

### Project Interface
```typescript
interface Project {
  id: string;                   // Unique identifier
  title: string;                // Project title
  description: string;          // Project description
  url?: string;                 // Repository URL
  liveUrl?: string;            // Live demo URL
  imageUrl?: string;           // Project image
}
```

### Experience Interface
```typescript
interface Experience {
  id: string;                   // Unique identifier
  role: string;                 // Job title
  company: string;              // Company name
  duration: string;             // Employment period
  description?: string;         // Job description
  location?: string;            // Work location
  companyUrl?: string;         // Company website
}
```

### Skill Interface
```typescript
interface Skill {
  id: string;                   // Unique identifier
  name: string;                 // Skill name
  category?: 'web-development' | 'mobile-development' | 'design' | 
            'data-science' | 'devops' | 'marketing' | 'business' | 'other';
}
```

## 🔐 Authentication API

### Firebase Auth Integration
```typescript
// Auth state management (stores/auth-store.ts)
interface AuthState {
  user: User | null;
  isLoaded: boolean;
  signIn: () => Promise<void>;
  signOut: () => Promise<void>;
}
```

#### Sign In
```typescript
const signIn = async (): Promise<void>
```
- **Purpose**: Authenticate user with Google OAuth
- **Process**: Firebase Auth popup flow
- **Usage**: Login page and auth guards

#### Sign Out
```typescript
const signOut = async (): Promise<void>
```
- **Purpose**: Sign out current user
- **Process**: Firebase Auth sign out
- **Usage**: Dashboard logout

## 📤 Export API

### Universal Export Hook (`hooks/useUniversalExport.ts`)

#### Export Portfolio
```typescript
const { exportPortfolio, isExporting, exportProgress } = useUniversalExport();

await exportPortfolio(portfolioData: PortfolioData): Promise<boolean>
```
- **Purpose**: Export portfolio as static website
- **Parameters**: `portfolioData` - Complete portfolio data
- **Returns**: Success boolean
- **Process**: Live DOM capture → HTML generation → ZIP download
- **Features**: Progress tracking, error handling

### Export Methods

#### Live DOM Capture Export
```typescript
export async function exportWithLiveDOMCapture(portfolioData: PortfolioData): Promise<boolean>
```
- **Purpose**: Primary export method using DOM capture
- **Process**: Capture live DOM → Clean → Generate HTML → ZIP
- **Advantages**: Pixel-perfect export

#### Template-based Export (Fallback)
```typescript
export async function exportPortfolioAsZip(portfolioData: PortfolioData): Promise<boolean>
```
- **Purpose**: Fallback export using templates
- **Process**: Generate HTML from data → Apply CSS → ZIP
- **Usage**: When DOM capture unavailable

## 🌐 External Service APIs

### Cloudinary Integration
```typescript
// Configuration
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your_upload_preset

// Upload endpoint
https://api.cloudinary.com/v1_1/{cloud_name}/upload
```
- **Purpose**: Image upload and optimization
- **Features**: Auto-optimization, format conversion, CDN delivery
- **Usage**: Profile images, project images, resume files

### Firebase Configuration
```typescript
// Environment variables
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

// Admin SDK (server-side)
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
```

## 🔒 Security & Validation

### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own portfolio
    match /portfolios/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Published portfolios are publicly readable
    match /portfolios/{userId} {
      allow read: if resource.data.isPublished == true;
    }
  }
}
```

### API Route Protection
```typescript
// Middleware authentication check
export async function middleware(request: NextRequest) {
  const token = request.cookies.get('auth-token');
  
  if (!token && request.nextUrl.pathname.startsWith('/portfolio')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
}
```

## 📈 Rate Limiting & Performance

### Current Limitations
- **Firestore**: 1 write per second per document
- **Cloudinary**: Based on plan limits
- **Vercel**: Function execution limits

### Optimization Strategies
- **Client-side Caching**: TanStack Query
- **Debounced Updates**: Editor auto-save
- **Batch Operations**: Multiple updates in single transaction
- **CDN Caching**: Static assets via Vercel Edge

## 🐛 Error Handling

### API Error Responses
```typescript
interface APIError {
  success: false;
  error: string;
  code?: string;
}

interface APISuccess<T> {
  success: true;
  data: T;
}
```

### Common Error Codes
- **AUTH_REQUIRED**: Authentication needed
- **PERMISSION_DENIED**: Insufficient permissions
- **NOT_FOUND**: Resource not found
- **VALIDATION_ERROR**: Invalid data format
- **RATE_LIMITED**: Too many requests

## 🔧 Development Tools

### API Testing
```bash
# Test theme compilation
curl -X POST http://localhost:3000/api/sync-themes

# Check portfolio data
# Use Firebase Console or browser DevTools
```

### Debug Mode
```typescript
// Enable API debugging
localStorage.setItem('api-debug', 'true');

// Firebase debugging
firebase.firestore().enableNetwork();
```

---

This API reference provides the foundation for understanding and extending Portfolio Builder's backend functionality. All APIs are designed with scalability, security, and developer experience in mind.
