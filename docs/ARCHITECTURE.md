# Profolify System Architecture

This document provides a comprehensive, end-to-end overview of the Profolify system architecture. It details the flow of data and control from user authentication to the final portfolio export, serving as a central reference for development.

## 1. High-Level Architecture

The system is a modern web application built on a **Jamstack architecture**, decoupling the frontend from the backend services. This results in a highly performant, scalable, and secure platform.

```mermaid
graph TD
    subgraph User Facing
        A[Next.js Frontend] --> B{Routing & Middleware};
        A --> C{Client-Side State};
    end

    subgraph Backend Services
        D[Firebase] --> E[Firestore Database];
        D --> F[Firebase Authentication];
        G[Cloudinary] --> H[Image & File Storage];
    end

    subgraph Build & Deployment
        I[Vercel] --> A;
    end

    B --> F;
    C --> A;
    A --> E;
    A --> H;
```

-   **Frontend**: A Next.js application responsible for all UI, including the dashboard, portfolio editor, and public portfolio pages.
-   **Backend Services**: A suite of managed services (Firebase, Cloudinary) provides the backend functionality, eliminating the need for a traditional monolithic server.
-   **Deployment**: Vercel provides seamless deployment, hosting, and a global CDN.

---

## 2. Authentication & Routing

The authentication flow is designed to be secure and seamless, leveraging Firebase for identity management and Next.js Middleware for route protection.

**Flow Diagram:**

```mermaid
sequenceDiagram
    participant User
    participant Middleware
    participant LoginPage
    participant Firebase

    User->>+Middleware: Requests /dashboard
    Middleware->>-User: No auth token, redirects to /login

    User->>+LoginPage: Clicks 'Continue with Google'
    LoginPage->>+Firebase: Initiates Google OAuth Popup
    Firebase-->>-LoginPage: Returns User object + idToken
    LoginPage->>LoginPage: Sets 'firebaseIdToken' cookie
    LoginPage->>-User: Redirects to /dashboard

    User->>+Middleware: Requests /dashboard (with cookie)
    Middleware->>-User: Auth token valid, allows access
```

**Key Components:**

1.  **Firebase Authentication**: Handles the entire OAuth 2.0 flow with Google, securely managing user credentials and sessions.

2.  **Login Page (`/app/(public)/login/page.tsx`)**: The entry point for authentication. On successful login, it receives an `idToken` from Firebase and, crucially, **sets it as a browser cookie** named `firebaseIdToken`.

3.  **Next.js Middleware (`/middleware.ts`)**: This is the gatekeeper. It runs on almost every request and inspects for the `firebaseIdToken` cookie.
    -   If a user tries to access a private route (e.g., `/dashboard`) **without** the cookie, it redirects them to `/login`.
    -   If a user tries to access a public route (e.g., `/login`) **with** the cookie, it redirects them to `/dashboard`.

4.  **Zustand Auth Store (`/stores/auth-store.ts`)**: A lightweight global store for the client-side. It holds the user's auth status (`user` object), which is used by components to conditionally render UI (e.g., showing a user's name in the header).

---

## 3. Data & State Management

The application uses a multi-layered approach to state management, choosing the right tool for each type of state.

-   **Server State (Remote Data)**: Managed by **TanStack Query**. It handles all fetching, caching, and synchronization of data from the Firestore database. This provides a robust, auto-updating data layer for the user's portfolio.

-   **Editor State (Local, Complex)**: Managed by **React Context + `useReducer`** (`/contexts/EditorContext.tsx`). This is ideal for the portfolio editor, where many fields and sub-components need to share and update a complex, nested data object. The reducer pattern ensures predictable state transitions.

-   **Global UI State (Simple)**: Managed by **Zustand** (`/stores/auth-store.ts`). Used for simple, global state like the user's authentication status. Its simplicity and minimal boilerplate make it perfect for this use case.

---

## 4. Theming Engine & Portfolio Editor

This is the core of the application, built on two innovative patterns: **Dual CSS Architecture** and the **Dual-Mode Component Pattern**.

### Dual CSS Architecture

-   **Development (`*-modular.css`)**: For a smooth developer experience, each theme component has its own `.css` file. These are imported into a central modular file, enabling hot-reloading.
-   **Production (`*-compiled.css`)**: For performance, a script (`/scripts/compile-themes.js`) bundles all modular CSS into a single compiled file. This file is used for the live portfolio and the final export.
-   **Syncing**: The `npm run sync-themes` command runs the compile script and copies the compiled CSS to the `/public` directory, making it web-accessible.

### Dual-Mode Component Pattern

This is the key to having a single set of theme components for both the editor and the live site.

1.  **The `isEditing` Prop**: Every theme component receives an `isEditing: boolean` prop.
2.  **Conditional Logic**: Inside the component, this prop is used to determine what to render:
    -   If `isEditing` is `true`, the component renders editable fields (using `<EditableText />`) and editor-specific UI (like image upload buttons). It gets its data from the `EditorContext`.
    -   If `isEditing` is `false`, the component renders standard, static HTML. It gets its data from `serverData` props.

**Example (`ModernHero.tsx`):**

```tsx
const data = isEditing && context ? context.state.formData : serverData!;

return (
    <EditableText isEditing={isEditing} initialValue={data.profession} ... />
    {isEditing && <EditorImageUploadButton ... />}
);
```

This elegant pattern ensures that editor-specific logic is completely isolated and never makes it into the final, public-facing portfolio.

---

## 5. Publishing & The Live DOM Export System

The export process is a standout feature, designed to create a perfect, static replica of the user's portfolio.

**Publishing Flow:**

1.  **User Clicks Publish**: A mutation is triggered in `app/(private)/portfolio/page.tsx`.
2.  **Theme Sync API**: The mutation first calls the `/api/sync-themes` endpoint to ensure the latest CSS changes are compiled and available.
3.  **Update Database**: The user's portfolio data in Firestore is updated, and the `isPublished` flag is set to `true`.

**Export Flow (`/lib/live-dom-capture.ts`):**

When a user clicks "Export", the system does **not** simply render from data. Instead, it performs a **Live DOM Capture**:

1.  **Hidden Iframe**: A hidden `<iframe>` is created in the browser.
2.  **Load Public URL**: The iframe loads the user's public portfolio URL.
3.  **Capture Rendered HTML**: Once loaded, the script captures the `outerHTML` of the theme's root element from the iframe. This captures the *exact* HTML and CSS as rendered by the browser, ensuring 100% fidelity.
4.  **Sanitize HTML**: The captured HTML is then meticulously cleaned:
    -   All editor-only attributes (`contenteditable`, etc.) are removed.
    -   Next.js-specific image URLs (`/_next/image/...`) are converted back to standard `src` attributes pointing to the original image on Cloudinary.
5.  **Package & Download**: The clean HTML is combined with the theme's compiled CSS into a single `index.html`. This file, along with any other assets, is zipped using `JSZip` on the client-side and provided to the user as a download.

This client-side export architecture is highly scalable, as it places zero load on the server.
