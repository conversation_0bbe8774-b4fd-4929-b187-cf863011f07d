# 🎨 Theme Development Guide

This guide provides a complete walkthrough for creating custom themes in Profolify. The theme system is designed to be fully manual, giving you complete control over every aspect of your theme's design and structure.

## 📁 Theme Architecture

Profolify uses a **modular CSS architecture** for clean, maintainable, and scalable themes.

-   **Theme Folder**: Each theme resides in its own folder inside `themes/` (e.g., `themes/sleek-theme`).
-   **Components Subdirectory**: Inside each theme folder, a `components` directory holds the theme's React components (`.tsx`) and their corresponding stylesheets (`.css`).
-   **Modular CSS File**: A main modular CSS file in the theme's root (e.g., `sleek-theme-modular.css`) uses `@import` to bundle all the component CSS files.
-   **Compiled CSS File**: The `npm run sync-themes` command automatically compiles your modular CSS into a single, optimized file for production (e.g., `sleek-theme-compiled.css`).

## 🚀 Manual Theme Creation: A Step-by-Step Guide

Here is the complete process for creating a new theme from scratch. We'll use a hypothetical "Sleek Theme" as an example.

### Step 1: Create the Directory Structure

First, manually create the necessary folders and empty files for your theme.

```bash
# Navigate to the themes directory
cd themes

# Create the main folder for your new theme
mkdir sleek-theme
cd sleek-theme

# Create the components subdirectory
mkdir components

# Create the initial files
touch sleek-theme-modular.css
touch components/SleekTheme.tsx
touch components/navbar.css
```

Your structure should look like this:

```
themes/
└── sleek-theme/
    ├── components/
    │   ├── SleekTheme.tsx
    │   └── navbar.css
    └── sleek-theme-modular.css
```

### Step 2: Write the Component CSS

Define the styles for your individual components. Prefix your classes with `theme-[theme-name]-` to avoid conflicts.

**Example: `themes/sleek-theme/components/navbar.css`**
```css
.theme-sleek-navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-sleek-navbar-list {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}
```

### Step 3: Create the Main React Component

This is the root component for your theme that assembles all the individual parts.

**Example: `themes/sleek-theme/components/SleekTheme.tsx`**
```tsx
"use client";
import { ProfolifyThemeProps } from "@/lib/types";

// This is a simplified example. You would create and import other components like Hero, About, etc.
const SleekNavbar = ({ serverData }) => (
    <div className="theme-sleek-navbar-container">
        <span>{serverData?.name || 'Your Name'}</span>
        <nav>
            <ul className="theme-sleek-navbar-list">
                <li>Home</li>
                <li>About</li>
                <li>Projects</li>
                <li>Contact</li>
            </ul>
        </nav>
    </div>
);

export function SleekTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-sleek-root">
            <SleekNavbar serverData={serverData} />
            {/* Other theme sections would go here */}
        </div>
    );
}
```

### Step 4: Import Styles in the Modular CSS

Open your theme's main modular CSS file and use `@import` to include the component styles you created.

**Example: `themes/sleek-theme/sleek-theme-modular.css`**
```css
/* Import all component styles for the Sleek Theme */
@import url('./components/navbar.css');
/* @import url('./components/hero.css'); */
/* @import url('./components/about.css'); */
```

### Step 5: Register the New Theme

Now, make the application aware of your new theme by adding it to the registry.

1.  **Import the Component**: Open `themes/theme-registry.ts` and import your main theme component.
2.  **Add to Registry**: Add a new configuration object for your theme to the `THEME_REGISTRY` array.

**Example: `themes/theme-registry.ts`**
```typescript
// ... other imports
import { SleekTheme } from './sleek-theme/components/SleekTheme'; // 1. Import your component

export const THEME_REGISTRY: ThemeConfig[] = [
    // ... other theme configs
    {
        id: 'sleek-theme-v1',
        name: 'Sleek',
        description: 'A sleek, modern design with gradient backgrounds and smooth animations.',
        cssFile: '/themes/sleek-theme/sleek-theme-compiled.css',
        sourceCssFile: 'themes/sleek-theme/sleek-theme-modular.css', // Important: path to your modular file
        component: SleekTheme, // 2. Add component here
        category: 'modern',
        version: '1.0.0',
        author: 'Your Name',
        preview: '/thumbnails/Sleek.png', // Add a preview image in /public/thumbnails
    },
];
```

### Step 6: Import Theme CSS in the Main Layout

To make your theme's styles available during development with live reloading, import its **modular** CSS file into the main application layout.

**Example: `app/layout.tsx`**
```tsx
// ... other imports
import "../themes/modern/modern-modular.css";
import "../themes/creative-minimalist/creative-minimalist-compiled.css";
import "../themes/sleek-theme/sleek-theme-modular.css"; // Add this line

// ... rest of the file
```

### Step 7: Compile and Test

Finally, run the `sync-themes` script. This compiles your modular CSS into a single `*-compiled.css` file, which is used in production and for the export functionality.

```bash
# Compile all themes
npm run sync-themes
```

Now, start your development server (`npm run dev`) and navigate to your dashboard. You should see your new "Sleek Theme" available to select and preview.

## ✅ Workflow Summary

1.  **Create Files**: Manually create the theme directory, component `.tsx` and `.css` files, and the main modular `.css` file.
2.  **Write Code**: Implement the React components and their corresponding CSS styles.
3.  **Import CSS**: Add `@import` rules to your main modular CSS file.
4.  **Register Theme**: Import your main component in `theme-registry.ts` and add a new entry to the `THEME_REGISTRY` array.
5.  **Import in Layout**: Add an import for your new modular CSS file in `app/layout.tsx` for development.
6.  **Compile**: Run `npm run sync-themes` to generate the production-ready compiled CSS file.
7.  **Test**: Run `npm run dev` and check your theme in the application.

## 📋 Advanced Theme Customization

### Adding Custom Components

You can add additional components beyond the standard ones:

#### 1. Create Custom CSS Component
```bash
# Create custom component CSS
touch themes/sleek-theme/components/testimonials.css
```

```css
/* sleek-theme Theme - Testimonials Component */

.theme-sleek-testimonials {
    padding: 5rem 0;
    background: #f8fafc;
}

.theme-sleek-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.theme-sleek-testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
```

#### 2. Add Component CSS File
Create your component CSS file in `themes/sleek-theme/components/`:

```css
/* themes/sleek-theme/components/custom-section.css */
.theme-sleek-custom-section {
    /* Your custom styles here */
@import url('./components/about.css');
@import url('./components/experience.css');
@import url('./components/skills.css');
@import url('./components/projects.css');
@import url('./components/contact.css');
@import url('./components/testimonials.css'); /* Add this line */
@import url('./components/footer.css');
```

#### 3. Create React Component
Create `themes/sleek-theme/components/SleekTestimonials.tsx`:

```tsx
"use client";
import { ProfolifyThemeProps } from "@/lib/types";

export function SleekTestimonials({ isEditing, serverData }: ProfolifyThemeProps) {
    return (
        <section className="theme-sleek-testimonials">
            <div className="theme-sleek-testimonials-container">
                <h2 className="theme-sleek-testimonials-title">
                    Testimonials
                </h2>
                <div className="theme-sleek-testimonials-grid">
                    {/* Add testimonial cards here */}
                </div>
            </div>
        </section>
    );
}
```

#### 4. Add to Main Theme Component
Update `SleekTheme.tsx`:

```tsx
import { SleekTestimonials } from "./SleekTestimonials";

export function SleekTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-sleek-root">
            <SleekNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <SleekHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <SleekAbout isEditing={isEditing} serverData={serverData} />
                <SleekExperience isEditing={isEditing} serverData={serverData} />
                <SleekSkills isEditing={isEditing} serverData={serverData} />
                <SleekProjects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <SleekTestimonials isEditing={isEditing} serverData={serverData} /> {/* Add this */}
                <SleekContact isEditing={isEditing} serverData={serverData} />
            </main>
            <SleekFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
}
```

### Performance Optimization

#### CSS Optimization Tips:
- Use CSS custom properties for consistent theming
- Minimize CSS specificity conflicts
- Use efficient selectors
- Optimize for mobile-first responsive design

```css
/* Use CSS custom properties */
.theme-sleek-root {
    --primary-color: #3b82f6;
    --secondary-color: #8b5cf6;
    --text-color: #1f2937;
    --background-color: #ffffff;
    --surface: #f9fafb;

    /* Define spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Define typography */
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
}

/* Use the custom properties */
.theme-sleek-btn-primary {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
}
```

#### 4. Editable Field Styling

**All themes must use certain editable field styling for consistency:**

1. Here each input field should have a dashed border to make it user think that it is editable and ensure consistency. Other style can be adjusted according to parent style bg and color contrast, but having a dashed border is mandatory.

```css
/* Editable Input Styling (only when contenteditable) */
.theme-creative-experience-field-input[contenteditable="true"] {
/*other styles*/    
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  outline: none;
  transition: all 0.3s ease;
  /*other styles*/    

}
```

```tsx
// ✅ CORRECT: Using editable field classes in the editable field component
   <EditableText
                isEditing={isEditing}
                tagName="div"
                className="theme-creative-experience-field-input"
                initialValue={experience.company}
                placeholder="Company Name"
                onSave={(value) => onUpdate(experience.id, 'company', value)}
            />
```

2. The EditableText component should be used to make the input field editable for every editable field. It takes values as props and returns the updated value.
The props values are:

| Prop | Type | Description |
| --- | --- | --- |
| `isEditing` | boolean | Whether the field is in editing mode |
| `tagName` | string | HTML tag name for the editable field |
| `className` | string | CSS class name for the editable field |
| `initialValue` | string | Initial value of the editable field |
| `placeholder` | string | Placeholder text for the editable field |
| `onSave` | function | Callback function to handle saving the updated value |


## 🎯 Best Practices

### CSS Best Practices

#### 1. Consistent Naming Convention
```css
/* Always prefix with theme name */
.theme-sleek-section { }
.theme-sleek-section-container { }
.theme-sleek-section-title { }
.theme-sleek-section-content { }
```

#### 2. Mobile-First Responsive Design
```css
/* Base styles for mobile */
.theme-sleek-hero-title {
    font-size: 2rem;
}

/* Tablet and up */
@media (min-width: 768px) {
    .theme-sleek-hero-title {
        font-size: 3rem;
    }
}

/* Desktop and up */
@media (min-width: 1024px) {
    .theme-sleek-hero-title {
        font-size: 4rem;
    }
}
```

#### 3. Use CSS Custom Properties
```css
.theme-sleek-root {
    /* Define theme colors */
    --primary: #3b82f6;
    --secondary: #8b5cf6;
    --accent: #10b981;
    --text: #1f2937;
    --background: #ffffff;
    --surface: #f9fafb;

    /* Define spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Define typography */
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
}

/* Use the custom properties */
.theme-sleek-btn-primary {
    background: var(--primary);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
}
```

#### 4. Consistent Component Structure
```css
/* Component wrapper */
.theme-sleek-[component] {
    /* Layout and positioning */
}

/* Component container */
.theme-sleek-[component]-container {
    /* Content width and centering */
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Component title */
.theme-sleek-[component]-title {
    /* Title styling */
}

/* Component content */
.theme-sleek-[component]-content {
    /* Content styling */
}
```

### React Component Best Practices

#### 1. Consistent Component Structure

##### All components should have a consistent structure. The structure should be as follows:
The main component has ProfolifyThemeProps as props which are:

| Prop | Type | Description |
| --- | --- | --- |
| `isEditing` | boolean | Whether the field is in editing mode |
| `serverData` | any | Server data for the component |
| `onImageUpload` | function | Callback function to handle image upload |

- OnImageUpload is a function can be used to upload images to the server. This can be used in the Hero component to upload the profile image or in the Project component to upload the project image. or any other component that requires image upload.
- The serverData prop is the data that is received from the server. it is an object that contains the data for the component.
- The isEditing prop is a boolean that is true when the component is in editing mode.

```tsx
"use client";
import { ProfolifyThemeProps } from "@/lib/types";
import { CreativeNavbar } from "./CreativeNavbar";
import { CreativeHero } from "./CreativeHero";
import { CreativeAbout } from "./CreativeAbout";
import { CreativeFooter } from "./CreativeFooter";

export function CreativeMinimalistTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {


    return (
        <div className="theme-creative-root">
            <CreativeNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <CreativeHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <CreativeAbout isEditing={isEditing} serverData={serverData} />
                {/* add more if you want or customize*/}
            </main>
            <CreativeFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
};

```




##### **Contact Section (Always Show with User Email)**
Contact section should always be visible with user's email pre-filled:

```typescript
// ✅ CORRECT: Contact Component
import { useAuthStore } from "@/stores/auth-store";

export function MyThemeContact({ isEditing, serverData }: SectionProps) {
    const { user } = useAuthStore();
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;

    // Use user's email as default if no email is set
    const defaultEmail = user?.email || '<EMAIL>';

    const contactItems = [
        {
            icon: Mail,
            label: "Email",
            value: data.email || defaultEmail, // Always has a value
            field: 'email' as keyof PortfolioData
        },
        {
            icon: Phone,
            label: "Phone",
            value: data.phone || 'Your phone number',
            field: 'phone' as keyof PortfolioData
        }
    ];

    return (
        <section className="theme-my-theme-contact">
            {/* Always show contact section */}
            <div className="contact-info">
                {contactItems.map((item) => {
                    // Always show email, only show phone if it has a real value
                    if (item.field === 'phone' && !item.value && !isEditing) return null;
                    if (item.field === 'phone' && item.value === 'Your phone number' && !isEditing) return null;

                    return <ContactItem key={item.field} item={item} />;
                })}
            </div>

            {/* Social links - only show if there are actual URLs */}
            {(isEditing || socialLinks.some(link => link.url)) && (
                <div className="social-section">
                    <h3>Follow Me</h3>
                    {socialLinks.map(social => (
                        <SocialLink key={social.field} social={social} />
                    ))}
                </div>
            )}
        </section>
    );
}
```

##### **❌ WRONG: Conditional Section Hiding**
```typescript
// ❌ DON'T DO THIS - Inconsistent behavior
export function BadExperience({ isEditing, serverData }: SectionProps) {
    const experiences = data.experiences || [];

    // ❌ This hides the section completely when empty
    if (!isEditing && experiences.length === 0) {
        return null;
    }

    return <section>...</section>;
}
```



## 🚀 Advanced Features

### Custom Animations

Add custom animations to your theme:

```css
/* themes/my-theme/components/animations.css */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.theme-my-theme-animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.theme-my-theme-animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}
```


### 3. Sync CSS Files

```bash
# Sync CSS files to public directory
npm run sync-themes

# Validate theme files
npm run validate-themes
```

## 🏗️ Theme Structure

### Recommended Modular Structure

```
themes/
├── theme-registry.ts
├── [theme-id]/
│   ├── [theme-id]-compiled.css # Auto-generated compiled CSS
│   └── components/
│       ├── navbar.css          # Navigation styles
│       ├── hero.css            # Hero section styles
│       ├── about.css           # About section styles
│       ├── experience.css      # Experience section styles
│       ├── skills.css          # Skills section styles
│       ├── projects.css        # Projects section styles
│       ├── contact.css         # Contact section styles
│       ├── footer.css          # Footer styles
│       ├── [Theme]Theme.tsx    # Main theme component
│       ├── [Theme]Navbar.tsx   # Navigation component
│       ├── [Theme]Hero.tsx     # Hero section
│       ├── [Theme]About.tsx    # About section
│       ├── [Theme]Experience.tsx
│       ├── [Theme]Skills.tsx
│       ├── [Theme]Projects.tsx
│       ├── [Theme]Contact.tsx
│       └── [Theme]Footer.tsx
```

### Example: Creative Minimalist Theme

```
themes/
├── theme-registry.ts
├── creative-minimalist/
│   ├── creative-minimalist-compiled.css # Auto-generated compiled CSS
│   └── components/
│       ├── navbar.css                   # Navigation styles
│       ├── hero.css                     # Hero section styles
│       ├── about.css                    # About section styles
│       ├── experience.css               # Experience section styles
│       ├── skills.css                   # Skills section styles
│       ├── projects.css                 # Projects section styles
│       ├── contact.css                  # Contact section styles
│       ├── footer.css                   # Footer styles
│       ├── CreativeMinimalistTheme.tsx  # Main theme component
│       ├── CreativeNavbar.tsx           # Navigation component
│       ├── CreativeHero.tsx             # Hero section
│       ├── CreativeAbout.tsx            # About section
│       ├── CreativeExperience.tsx       # Experience section
│       ├── CreativeSkills.tsx           # Skills section
│       ├── CreativeProjects.tsx         # Projects section
│       ├── CreativeContact.tsx          # Contact section
│       └── CreativeFooter.tsx           # Footer component
```


### 5. CSS File Management for Export Consistency

#### Important: CSS File Synchronization

The theme CSS files must be properly synchronized between the `themes/` directory and the `public/themes/` directory to ensure consistent export functionality:

1. **Development Location**: `themes/your-theme/your-theme.css`
2. **Export Location**: `public/themes/your-theme/your-theme.css`

#### Automatic Synchronization

Use the sync script to copy CSS files to the public directory:

```bash
npm run sync-themes
```

This ensures that:
- Live editor uses the development CSS files
- Static export uses the public CSS files
- Both versions remain identical

#### Manual CSS Management

If you need to manually manage CSS files:

1. **After editing CSS**: Always run `npm run sync-themes`
2. **Before deployment**: Verify all CSS files are synced
3. **For new themes**: Ensure CSS files are copied to public directory

## ⚛️ Component Guidelines

### 1. Use Pure HTML/CSS
All components must use pure HTML elements with CSS classes:

```tsx
// ✅ Good
<button className="theme-elegant-portfolio-btn theme-elegant-portfolio-btn-primary">
  Click me
</button>

// ❌ Bad (ShadCN components won't export properly)
<Button variant="primary">Click me</Button>
```

### 2. Component Structure
Each component should follow this pattern:

```tsx
"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";

export function ElegantPortfolioHero({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;

    return (
        <section className="theme-elegant-portfolio-hero">
            <div className="theme-elegant-portfolio-container">
                <EditableText
                    isEditing={isEditing}
                    tagName="h1"
                    className="theme-elegant-portfolio-hero-title"
                    initialValue={data.userName}
                    onSave={(value) => {/* handle save */}}
                />
            </div>
        </section>
    );
}
```


## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run sync-themes` | Universal compiler: compile all themes + sync to public directory |
| `npm run validate-themes` | Validate theme files and structure |
| `npm run dev` | Start development server |
| `npm run build` | Build for production (auto-compiles and syncs themes) |

## 🔄 Dual CSS Architecture Workflow

### Development vs Export CSS System

Profolify uses a **dual CSS architecture** to optimize both development experience and export functionality:

- **Development**: Uses modular CSS with `@import` statements for easy editing
- **Export**: Uses compiled CSS with all styles inline for static site compatibility

### Development Workflow

1. **Edit Component CSS Files Directly**
   ```bash
   # Edit individual component files - changes are immediate
   themes/your-theme/components/navbar.css
   themes/your-theme/components/hero.css
   themes/your-theme/components/experience.css
   # ... etc
   ```

2. **Compile and Sync for Export Testing**
   ```bash
   npm run sync-themes
   ```
   This automatically:
   - Compiles modular CSS into a single file
   - Syncs the compiled CSS to `public/themes/your-theme/`

3. **Test Changes**
   ```bash
   npm run dev
   ```

### CSS Compilation Process

The system automatically handles CSS compilation:

```bash
# Universal compilation (compile all themes + sync to public)
npm run sync-themes

# Validate themes only (no compilation)
npm run validate-themes

# Production build (compile + sync + build)
npm run build
```

### How It Works

1. **Component CSS Files** (`components/*.css`):
   - Individual CSS files for each theme section

When a user exports their portfolio, a process called **Live DOM Capture** is initiated. Here’s how it works:

1.  **Hidden Iframe**: The system loads the user's public portfolio URL in a hidden iframe.
2.  **DOM Capture**: It captures the fully rendered HTML of your theme from the iframe.
3.  **HTML Cleanup**: It meticulously cleans the captured HTML:
    -   Removes all `contenteditable` attributes.
    -   Deletes any elements with editor-specific data attributes.
    -   Converts Next.js optimized images (`/_next/image/...`) back into standard `<img>` tags with direct URLs.
4.  **Packaging**: It combines the cleaned HTML with the theme's **compiled CSS** into a self-contained `index.html` file and packages it in a `.zip` archive.

Because of this process, you do not need to write any special code for the export. As long as you follow the dual-mode component pattern and keep editor logic isolated, the export will work automatically.

## Guidelines & Best Practices

-   **Use `EditableText`**: Always use the `<EditableText />` component for any text content that the user should be able to modify. It handles HTML stripping and data saving automatically.
-   **Semantic HTML**: Write clean, semantic HTML. This improves accessibility and makes your theme easier to maintain.
-   **No JS-Reliant UI**: Avoid using UI libraries or patterns that require JavaScript to render. The exported site is static HTML and CSS; complex JavaScript will not run.
-   **Keep CSS Modular**: Continue to create separate `.css` files for each component. It keeps the codebase clean.
-   **Sync Often**: Run `npm run sync-themes` regularly to ensure your compiled CSS is up-to-date.

### Benefits of This Approach

- ✅ **Best of Both Worlds**: Easy development + reliable exports
- ✅ **Immediate Changes**: Edit CSS and see changes instantly in development
- ✅ **Export Compatibility**: Compiled CSS works in static sites
- ✅ **Automatic Process**: Compilation happens automatically
- ✅ **Future-Proof Architecture**: New themes work with export system automatically
- ✅ **Simplified Development**: Focus on creating beautiful themes, not export compatibility
- ✅ **Easy Debugging**: Each component's styles are in separate files
- ✅ **Better Organization**: Clear separation of section styles
- ✅ **Scalable**: Easy to add new sections or modify existing ones
- ✅ **Maintainable**: Find and edit specific styles quickly
- ✅ **Collaborative**: Multiple developers can work on different sections

## 🎯 Key Guidelines for Theme Development


### File Organization Tips

1. **Keep CSS files focused**: Each component CSS file should only contain styles for that specific section
2. **Use consistent naming**: Follow the pattern `[section].css` (e.g., `hero.css`, `experience.css`)
3. **Import order matters**: Import CSS files in logical order in your main modular CSS file
4. **Comment your code**: Add comments to explain complex styles or design decisions


### Common Export Issues and Solutions

**Issue: Images show as broken in exported site**
- **Solution**: Use `PortfolioImage` component instead of regular `<img>` tags
- **Check**: Verify `isEditing` prop is passed to `PortfolioImage`

**Issue: Mobile menu doesn't work in exported site**
- **Solution**: Follow export-compatible mobile menu pattern
- **Check**: Ensure `data-mobile-menu-toggle` and `data-target` attributes are set

**Issue: Layout has unwanted margins in exported site**
- **Solution**: Export system includes CSS reset, check for conflicting styles
- **Check**: Verify theme root container has proper CSS classes

**Issue: CSS styles not applied in exported site**
- **Solution**: Run `npm run sync-themes` and verify CSS file exists in public directory
- **Check**: Ensure theme is properly registered in theme registry

## 🎯 Export Compatibility - Live DOM Capture System

Profolify uses a revolutionary **Live DOM Capture Export System** that captures the actual rendered DOM from your live theme, ensuring pixel-perfect exports. To ensure your theme works perfectly with this system:

### Core Requirements

1. **Use Pure HTML/CSS**: No ShadCN or external component libraries
2. **Self-contained CSS**: All styles must be in the theme's CSS file
3. **PortfolioImage Component**: Use `PortfolioImage` for all images to ensure export compatibility
4. **Mobile Menu Compatibility**: Follow mobile menu patterns for export functionality
5. **Theme Root Container**: Use proper theme root class naming

### Image Handling for Export

**✅ Correct Image Usage:**
```tsx
import { PortfolioImage } from "@/components/ui/PortfolioImage";

// Use PortfolioImage component for all images
<PortfolioImage
    isEditing={isEditing}
    src={data.profileImageUrl || 'https://placehold.co/400x400/f3f4f6/6b7280?text=Profile'}
    alt={data.userName}
    width={400}
    height={400}
    className="theme-your-theme-hero-image"
/>
```

**❌ Incorrect Image Usage:**
```tsx
// Don't use regular img tags directly
<img src={data.profileImageUrl} alt={data.userName} />

// Don't use Next.js Image component directly
<Image src={data.profileImageUrl} alt={data.userName} width={400} height={400} />
```

### Mobile Menu Export Compatibility

**✅ Export-Compatible Mobile Menu:**
```tsx
import { useIsExport } from "@/contexts/ExportContext";

export function YourThemeNavbar({ isEditing, serverData }: SectionProps) {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const isExport = useIsExport();

    return (
        <nav className="theme-your-theme-navbar">
            {/* Mobile menu toggle button */}
            <button
                type="button"
                onClick={isExport ? undefined : () => setIsMobileMenuOpen(!isMobileMenuOpen)}
                aria-label="Toggle menu"
                className="theme-your-theme-navbar-mobile-btn"
                {...(isExport && {
                    'data-mobile-menu-toggle': 'true',
                    'data-target': 'your-theme-mobile-menu'
                })}
            >
                <svg>...</svg>
            </button>

            {/* Mobile menu */}
            <div
                id="your-theme-mobile-menu"
                className={`theme-your-theme-navbar-mobile-menu ${!isExport && isMobileMenuOpen ? 'active' : ''}`}
                style={isExport ? { display: 'none' } : undefined}
            >
                <nav>...</nav>
            </div>
        </nav>
    );
}
```

### Theme Root Container

**✅ Proper Theme Root:**
```tsx
export function YourTheme({ isEditing, serverData, onImageUpload }: ThemeProps) {
    return (
        <div className="theme-your-theme-root">
            <YourThemeNavbar isEditing={isEditing} serverData={serverData} />
            <YourThemeHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
            {/* ... other sections */}
        </div>
    );
}
```

### CSS Classes for Export

Ensure your CSS follows the theme-specific naming pattern:

```css
/* Theme root container */
.theme-your-theme-root {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Mobile menu styles */
.theme-your-theme-navbar-mobile-menu {
    display: none;
}

.theme-your-theme-navbar-mobile-menu.active {
    display: block;
}
```

## 🚀 Advanced Features

### Custom Animations
```css
@keyframes theme-elegant-portfolio-fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.theme-elegant-portfolio-hero {
  animation: theme-elegant-portfolio-fade-in 0.6s ease-out;
}
```



### Print Styles
```css
@media print {
  .theme-elegant-portfolio-navbar {
    display: none;
  }
}
```

## 🎯 Live DOM Capture System Benefits

The Live DOM Capture Export System provides significant advantages for theme developers:

### ✅ Theme Development Benefits

**1. Perfect Export Fidelity**
- Your exported themes match the live site exactly, pixel for pixel
- No need to maintain separate export templates
- Complex layouts and animations are preserved automatically

**2. Future-Proof Architecture**
- New themes work with export system automatically
- No additional export logic required per theme
- System scales infinitely with new theme additions

**3. Simplified Development**
- Focus on creating beautiful themes, not export compatibility
- Standard React components work seamlessly
- No special export considerations beyond basic guidelines

**4. Comprehensive Image Handling**
- 6-layer image fixing system ensures all images work in exports
- Automatic conversion of Next.js optimized URLs to direct URLs
- Robust error handling with automatic fallbacks

**5. Native Mobile Menu Support**
- Export system automatically adds JavaScript for mobile menus
- No external dependencies required
- Touch-friendly navigation in exported sites

### ✅ Technical Excellence

**Performance:**
- Sub-second export generation
- Client-side processing eliminates server timeouts
- Instant feedback and debugging capabilities

**Reliability:**
- Multiple fallback layers for error handling
- Comprehensive logging for debugging
- Cross-browser compatibility guaranteed

**Maintainability:**
- Single codebase for live and export versions
- Clean separation of concerns
- Easy to extend and modify

### ✅ User Experience

**For Theme Developers:**
- Simple development workflow
- Immediate export testing
- Clear debugging information

**For End Users:**
- Professional-quality exported sites
- Perfect mobile responsiveness
- Self-contained, deployable packages

This revolutionary system ensures that your themes provide the best possible experience for both development and export, making Profolify the premier platform for portfolio creation.

`

## 📋 Complete Workflow Summary

### Quick Reference: Creating a New Theme

```bash
# 1. Create theme directory and files
mkdir themes/sleek-theme
cd themes/sleek-theme
mkdir components
touch sleek-theme-modular.css
touch components/SleekTheme.tsx
touch components/navbar.css

# 2. Write component CSS and React components
# Implement your theme's styles and components

# 3. Import styles in modular CSS
# Add @import rules to sleek-theme-modular.css

# 4. Register theme
# Import SleekTheme in theme-registry.ts and add to THEME_REGISTRY

# 5. Import in layout
# Add import for sleek-theme-modular.css in app/layout.tsx

# 6. Compile and test
npm run sync-themes
npm run dev
```

### File Checklist for New Theme

**Required Files:**
- ✅ `themes/[theme-name]/components/[ThemeName]Theme.tsx` (main component)
- ✅ `themes/[theme-name]/components/[component-name].css` (individual component styles)
- ✅ `themes/[theme-name]/[theme-name]-modular.css` (main modular CSS file)

**Auto-Generated Files:**
- ✅ `themes/[theme-name]/[theme-name]-compiled.css` (created by universal compiler)
- ✅ `public/themes/[theme-name]/[theme-name]-compiled.css` (synced automatically)
