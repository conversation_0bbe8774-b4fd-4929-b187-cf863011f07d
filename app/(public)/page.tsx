import { Metadata } from "next";
import { LandingNavbar } from "@/components/landing/navbar";
import { HeroSection } from "@/components/landing/hero-section";
import { LandingFooter } from "@/components/landing/footer";
import { FeaturesSection } from "@/components/landing/features-section";
import { ThemesSection } from "@/components/landing/themes-section";
import { PricingSection } from "@/components/landing/pricing-section";
import { FAQSection } from "@/components/landing/faq-section";
import { SEOContentSection } from "@/components/landing/seo-content-section";

export const metadata: Metadata = {
  title: "Free Portfolio Builder | Create Professional Portfolios & Export Static HTML - Profolify",
  description: "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
  keywords: [
    "free portfolio builder", "portfolio maker", "online portfolio builder", "professional portfolio builder",
    "export portfolio HTML", "static HTML portfolio", "portfolio website builder", "resume portfolio",
    "no code portfolio", "drag and drop portfolio", "WYSIWYG portfolio editor", "portfolio themes",
    "mobile responsive portfolio", "SEO optimized portfolio", "developer portfolio", "designer portfolio"
  ],
  openGraph: {
    title: "Free Portfolio Builder | Create Professional Portfolios & Export Static HTML",
    description: "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
    type: "website",
    url: "https://profolify.com",
  },
  twitter: {
    card: "summary_large_image",
    title: "Free Portfolio Builder | Create Professional Portfolios & Export Static HTML",
    description: "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
  },
  alternates: {
    canonical: "https://profolify.com",
  },
};

export default function LandingPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Profolify - Free Portfolio Builder",
    "description": "Build stunning professional portfolios with our free portfolio builder. Export as static HTML files, choose from beautiful themes, and showcase your work with SEO-optimized portfolios. No coding required.",
    "url": "https://profolify.com",
    "applicationCategory": "WebApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Free Portfolio Builder",
      "Static HTML Export",
      "Professional Themes",
      "Mobile Responsive Design",
      "SEO Optimized Portfolios",
      "No Coding Required",
      "WYSIWYG Editor",
      "Resume Integration"
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="flex flex-col min-h-screen bg-backgroundPrimary">
        <LandingNavbar />
        <main className="flex-1">
          <HeroSection />
          <FeaturesSection />
          <ThemesSection />
          <SEOContentSection />
          <PricingSection />
          <FAQSection />
        </main>
        <LandingFooter />
      </div>
    </>
  );
}
