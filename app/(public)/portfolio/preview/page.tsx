"use client";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Smartphone, Tablet, Monitor } from "lucide-react";
import { cn } from "@/lib/utils";
import { PortfolioData } from "@/lib/types";

// --- NEW: Import theme registry ---
import { getThemeComponent } from "@/themes/theme-registry";

export default function PortfolioPreviewPage() {
  const [data, setData] = useState<PortfolioData | null>(null);
  const [view, setView] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  useEffect(() => {
    // 1. Get the preview data from localStorage (no change here)
    const previewData = localStorage.getItem('portfolio-preview');
    if (previewData) {
      setData(JSON.parse(previewData));
    }
  }, []);

  // Add effect to handle mobile menu simulation and disable interactions
  useEffect(() => {
    const timer = setTimeout(() => {
      const previewContainer = document.querySelector('.preview-container');
      if (!previewContainer) return;

      // Disable all interactive elements in preview
      const interactiveElements = previewContainer.querySelectorAll(
        'button, a, input, textarea, select, [role="button"], [tabindex]'
      );

      interactiveElements.forEach(element => {
        // Disable pointer events and tabbing
        (element as HTMLElement).style.pointerEvents = 'none';
        (element as HTMLElement).style.cursor = 'default';
        element.setAttribute('tabindex', '-1');

        // Prevent any click events
        element.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
        }, true);
      });

      if (view === 'mobile' || view === 'tablet') {
        // Show mobile menu button but make it non-interactive
        const mobileMenuContainers = previewContainer.querySelectorAll('.md\\:hidden');
        const desktopNavs = previewContainer.querySelectorAll('.hidden.md\\:flex');

        mobileMenuContainers.forEach(container => {
          (container as HTMLElement).style.display = 'block';
        });

        desktopNavs.forEach(nav => {
          (nav as HTMLElement).style.display = 'none';
        });
      } else {
        // Desktop view
        const desktopNavs = previewContainer.querySelectorAll('.hidden.md\\:flex');
        const mobileMenuContainers = previewContainer.querySelectorAll('.md\\:hidden');

        desktopNavs.forEach(nav => {
          (nav as HTMLElement).style.display = 'flex';
        });

        mobileMenuContainers.forEach(container => {
          (container as HTMLElement).style.display = 'none';
        });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [view]);

  // Show a loader while data is being loaded from localStorage
  if (!data) {
    return <div className="flex h-screen items-center justify-center bg-muted">Loading Preview...</div>;
  }

  const viewClasses = {
    desktop: 'w-full h-full',
    tablet: 'w-[768px] h-[90%] rounded-lg shadow-2xl', // Exact tablet breakpoint
    mobile: 'w-[375px] h-[90%] rounded-lg shadow-2xl', // Standard mobile width
  };

  const renderTheme = () => {
    const ThemeComponent = getThemeComponent(data.templateId);
    if (!ThemeComponent) {
      // Fallback to Creative Minimalist if theme not found
      const FallbackComponent = getThemeComponent('creative-theme-v1');
      return FallbackComponent ? <FallbackComponent isEditing={false} serverData={data} /> :
        <div>Theme not found</div>;
    }
    return <ThemeComponent isEditing={false} serverData={data} />;
  };

  return (
    <>
      {/* Custom CSS for responsive preview simulation */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Mobile preview - force all responsive classes to mobile state */
          .preview-mobile .sm\\:grid-cols-2 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .sm\\:grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .lg\\:grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .lg\\:grid-cols-4 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .xl\\:grid-cols-4 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .\\32 xl\\:grid-cols-4 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .md\\:grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
          .preview-mobile .sm\\:text-xl { font-size: 1rem !important; line-height: 1.5rem !important; }
          .preview-mobile .lg\\:text-2xl { font-size: 1.125rem !important; line-height: 1.75rem !important; }
          .preview-mobile .xl\\:text-3xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
          .preview-mobile .sm\\:text-3xl { font-size: 1.5rem !important; line-height: 2rem !important; }
          .preview-mobile .md\\:text-4xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
          .preview-mobile .lg\\:text-5xl { font-size: 2.25rem !important; line-height: 2.5rem !important; }
          .preview-mobile .sm\\:p-6 { padding: 1rem !important; }
          .preview-mobile .lg\\:p-8 { padding: 1rem !important; }
          .preview-mobile .xl\\:p-12 { padding: 1rem !important; }
          .preview-mobile .sm\\:gap-6 { gap: 1rem !important; }
          .preview-mobile .lg\\:gap-8 { gap: 1rem !important; }
          .preview-mobile .sm\\:gap-8 { gap: 1.5rem !important; }
          .preview-mobile .lg\\:gap-10 { gap: 1.5rem !important; }
          .preview-mobile .sm\\:mb-3 { margin-bottom: 0.5rem !important; }
          .preview-mobile .sm\\:mb-6 { margin-bottom: 1rem !important; }
          .preview-mobile .sm\\:mb-8 { margin-bottom: 1.5rem !important; }
          .preview-mobile .sm\\:mb-12 { margin-bottom: 2rem !important; }
          .preview-mobile .sm\\:space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem !important; }
          .preview-mobile .sm\\:space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem !important; }

          /* Mobile Navigation Overrides */
          .preview-mobile .hidden.md\\:flex { display: none !important; }
          .preview-mobile .md\\:hidden { display: block !important; }
          .preview-mobile .hidden.sm\\:flex { display: none !important; }
          .preview-mobile .sm\\:hidden { display: block !important; }

          /* Tablet preview - force tablet responsive state */
          .preview-tablet .sm\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .lg\\:grid-cols-3 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .lg\\:grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .xl\\:grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .\\32 xl\\:grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
          .preview-tablet .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
          .preview-tablet .sm\\:text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
          .preview-tablet .lg\\:text-2xl { font-size: 1.5rem !important; line-height: 2rem !important; }
          .preview-tablet .xl\\:text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
          .preview-tablet .sm\\:text-3xl { font-size: 1.875rem !important; line-height: 2.25rem !important; }
          .preview-tablet .md\\:text-4xl { font-size: 2.25rem !important; line-height: 2.5rem !important; }
          .preview-tablet .lg\\:text-5xl { font-size: 3rem !important; line-height: 1 !important; }
          .preview-tablet .sm\\:p-6 { padding: 1.5rem !important; }
          .preview-tablet .lg\\:p-8 { padding: 1.5rem !important; }
          .preview-tablet .xl\\:p-12 { padding: 1.5rem !important; }
          .preview-tablet .sm\\:gap-6 { gap: 1.5rem !important; }
          .preview-tablet .lg\\:gap-8 { gap: 1.5rem !important; }
          .preview-tablet .sm\\:gap-8 { gap: 2rem !important; }
          .preview-tablet .lg\\:gap-10 { gap: 2rem !important; }
          .preview-tablet .sm\\:mb-3 { margin-bottom: 0.75rem !important; }
          .preview-tablet .sm\\:mb-6 { margin-bottom: 1.5rem !important; }
          .preview-tablet .sm\\:mb-8 { margin-bottom: 2rem !important; }
          .preview-tablet .sm\\:mb-12 { margin-bottom: 3rem !important; }
          .preview-tablet .sm\\:space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1.5rem !important; }
          .preview-tablet .sm\\:space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem !important; }

          /* Tablet Navigation Overrides */
          .preview-tablet .hidden.md\\:flex { display: none !important; }
          .preview-tablet .md\\:hidden { display: block !important; }

          /* Preview Container - Disable all interactions */
          .preview-container {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
          }

          .preview-container * {
            pointer-events: none !important;
            cursor: default !important;
          }

          /* Visual indication that it's a preview */
          .preview-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent;
            z-index: 9999;
            pointer-events: none;
          }
        `
      }} />

      <div className="flex flex-col h-screen bg-muted">
        <header className="flex justify-center items-center p-2 bg-background border-b gap-2">
          <Button variant={view === 'mobile' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('mobile')}><Smartphone /></Button>
          <Button variant={view === 'tablet' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('tablet')}><Tablet /></Button>
          <Button variant={view === 'desktop' ? 'secondary' : 'ghost'} size="icon" onClick={() => setView('desktop')}><Monitor /></Button>
        </header>
      <main className="flex-1 p-4 flex justify-center items-center overflow-hidden">
        <div className={cn("bg-background transition-all duration-300 ease-in-out", viewClasses[view])}>
          <div className={cn(
            "w-full h-full overflow-y-auto preview-container",
            // Apply responsive simulation classes
            view === 'mobile' && "preview-mobile",
            view === 'tablet' && "preview-tablet",
            view === 'desktop' && "preview-desktop"
          )}>
            {/* 3. Render the dynamically selected theme */}
            {renderTheme()}
          </div>
        </div>
      </main>
      </div>
    </>
  );
}