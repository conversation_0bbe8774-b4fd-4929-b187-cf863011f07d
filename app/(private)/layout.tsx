"use client";
import { useAuthStore } from "@/stores/auth-store";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

function PrivateLayoutContent({ children }: { children: React.ReactNode }) {
  const { user, isLoaded } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && !user) {
      router.replace("/login");
    }
  }, [user, isLoaded, router]);


  return (
    <main className="min-h-screen bg-muted/40">
        {children}
    </main>
  );
}

export default function PrivateLayout({ children }: { children: React.ReactNode }) {
  return (
      <PrivateLayoutContent>{children}</PrivateLayoutContent>
  );
}