"use client";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { LogOut, LayoutDashboard, Rocket, Trash2, Loader2, Home } from "lucide-react";
import Link from "next/link";
import { useAuthStore } from "@/stores/auth-store";
import { auth } from "@/lib/firebase";
import { signOut } from "firebase/auth";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import Image from "next/image";

interface EditorHeaderProps {
  isPublishing: boolean;
  isDeleting: boolean;
  isDirty: boolean; // Keep for publish button logic
  onDelete: () => void;
  onTogglePublish: () => void;
}

// Helper function to delete cookies
const deleteCookie = (name: string) => {
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
};

export function EditorHeader(props: EditorHeaderProps) {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const [isSigningOut, setIsSigningOut] = useState(false);

  // Sign out handler
  const handleSignOut = async () => {
    setIsSigningOut(true);

    // Add timeout protection - if logout takes more than 3 seconds, force redirect
    const timeoutId = setTimeout(() => {
      console.warn("Logout timeout - forcing redirect");
      deleteCookie('firebaseIdToken');
      window.location.href = "/login";
    }, 3000);

    try {
      // Clear cookie immediately
      deleteCookie('firebaseIdToken');

      // Start Firebase signOut but don't wait for it
      signOut(auth).catch((err: unknown) => console.warn("Firebase signOut error:", err));

      // Clear query cache but don't wait for it
      queryClient.clear();

      // Clear the timeout since we're redirecting
      clearTimeout(timeoutId);

      // Force immediate redirect using window.location for reliability
      window.location.href = "/login";

    } catch (error) {
      console.error("Sign out error:", error);
      clearTimeout(timeoutId);
      deleteCookie('firebaseIdToken');
      // Force redirect even on error
      window.location.href = "/login";
    }
  };

  const EditorBranding = () => (
    <div className="flex items-center gap-5">
      <div className="flex items-center gap-4">
        {/* <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center">
          <span className="text-white font-bold text-sm">P</span>
        </div> */}
        <Link
          href="/"
          className="flex items-center gap-2 relative z-10"
        >
          {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div> */}
          <Image src="/icon.svg" alt="logo" width={32} height={32} />
          <span className="font-bold text-xl lg:text-2xl gradient-text">
            Profolify
          </span>
        </Link>
        <div className="flex flex-col items-start">
          <h1 className="text-sm font-bold text-slate-800">Portfolio Editor</h1>
          <p className="text-xs text-slate-500">Design your professional presence</p>
        </div>
      </div>
    </div>
  );

  const ActionButtons = () => (
    <div className="flex items-center gap-3">
      <Button asChild variant="outline" className="bg-white hover:bg-slate-50 border-slate-200 text-sm">
        <Link href="/dashboard">
          <Home className="mr-2 h-4 w-4" />
          Dashboard
        </Link>
      </Button>
      <Button variant="outline" onClick={props.onDelete} disabled={props.isDeleting} className="bg-white hover:bg-red-50 border-red-200 text-red-600 hover:text-red-700 text-sm">
        {props.isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2 className="mr-2 h-4 w-4" />}
        {props.isDeleting ? 'Discarding...' : 'Discard'}
      </Button>
      <Button
        onClick={props.onTogglePublish}
        disabled={props.isPublishing}
        className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg"
      >
        {props.isPublishing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Rocket className="mr-2 h-4 w-4" />}
        {props.isPublishing ? "Publishing..." : "Publish"}
      </Button>
    </div>
  );

  const UserMenu = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="p-0 h-10 w-10 rounded-full hover:bg-slate-100">
          <Avatar className="h-10 w-10 border-2 border-slate-200 hover:border-blue-400 transition-colors">
            <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName || 'User'} />
            <AvatarFallback className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold">
              {user?.displayName?.charAt(0)?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56 z-[9999] bg-white border-slate-200 shadow-xl">
        <div className="flex flex-col px-3 py-3 bg-slate-50 border-b border-slate-200">
          <span className="font-semibold text-slate-800 truncate">{user?.displayName || 'User'}</span>
          <span className="text-xs text-slate-500 truncate">{user?.email || 'No email'}</span>
        </div>
        <DropdownMenuItem asChild className="hover:bg-blue-50">
          <Link href="/dashboard">
            <LayoutDashboard className="mr-2 h-4 w-4 hover:text-white" />
            Dashboard
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator className="bg-slate-200" />
        <DropdownMenuItem onClick={handleSignOut} disabled={isSigningOut} className="hover:bg-red-50 text-red-600">
          {isSigningOut ? <Loader2 className="mr-2 h-4 w-4  animate-spin" /> : <LogOut className="mr-2 h-4 w-4 hover:text-white" />}
          {isSigningOut ? 'Signing out...' : 'Sign out'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <header className="sticky top-0 z-50  w-full bg-white/95 backdrop-blur-sm border-b border-slate-200 shadow-sm">
      <div className="flex justify-between mx-auto px-8 py-4  gap-4">
        <EditorBranding />
        <div className="flex items-center gap-3">
          <ActionButtons />
          <UserMenu />
        </div>
      </div>
    </header>
  );
}
