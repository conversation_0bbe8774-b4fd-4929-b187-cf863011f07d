import { doc, getDoc, setDoc, deleteDoc, collection, query, where, getDocs, limit } from "firebase/firestore";
import { firestore } from "./firebase";
import { PortfolioData, defaultPortfolioData } from "./types";
import { slugify } from "./utils";

// --- MODIFIED: Renamed to getPortfolio and returns null if not found ---
export const getPortfolio = async (userId: string): Promise<PortfolioData | null> => {
    const docRef = doc(firestore, "portfolios", userId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
        return docSnap.data() as PortfolioData;
    } else {
        return null;
    }
};


// --- Function to generate a unique slug ---
export const generateUniqueSlug = async (displayName: string, userId: string): Promise<string> => {
    const baseSlug = slugify(displayName);
    let finalSlug = baseSlug;
    let counter = 2;

    // This loop will continue until it finds a slug that is not in use
    while (true) {
        const q = query(
            collection(firestore, "portfolios"),
            where("slug", "==", finalSlug)
        );
        const querySnapshot = await getDocs(q);

        // If the slug doesn't exist, we can use it!
        if (querySnapshot.empty) {
            break;
        }

        // If the slug exists, we need to check if it belongs to the current user.
        // If it does, they can keep their slug.
        const slugIsOwn = querySnapshot.docs.some(doc => doc.id === userId);
        if (slugIsOwn) {
            break;
        }

        // If the slug exists and belongs to someone else, append the counter.
        finalSlug = `${baseSlug}-${counter}`;
        counter++;
    }

    return finalSlug;
};


// --- NEW: Function to create a portfolio from a selected template ---
export const createPortfolioFromTemplate = async ({ userId, userEmail, templateId }: { userId: string; userEmail: string | null; templateId: string; }) => {
    const docRef = doc(firestore, "portfolios", userId);
    const newPortfolio = { ...defaultPortfolioData(userId, userEmail), templateId };
    await setDoc(docRef, newPortfolio);
    return newPortfolio;
}

// Update portfolio data (no change)
export const updatePortfolio = async ({ userId, data }: { userId: string; data: Partial<PortfolioData> }) => {
    const docRef = doc(firestore, "portfolios", userId);
    await setDoc(docRef, data, { merge: true });
};

// Delete a portfolio (no change)
export const deletePortfolio = async (userId: string) => {
    const docRef = doc(firestore, "portfolios", userId);
    await deleteDoc(docRef);
};

// Get a portfolio by its public slug (no change)
export const getPortfolioBySlug = async (slug: string): Promise<PortfolioData | null> => {
    const q = query(
        collection(firestore, 'portfolios'),
        where("slug", "==", slug),
        where("isPublished", "==", true),
        limit(1)
    );
    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) return null;
    return querySnapshot.docs[0].data() as PortfolioData;
};

// Upload a file to Cloudinary (no change)
export const uploadFile = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET!);

    const response = await fetch(`https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/upload`, {
        method: 'POST',
        body: formData,
    });

    if (!response.ok) throw new Error('File upload failed');
    const data = await response.json();
    return data.resource_type === 'image' ? data.secure_url.replace('/upload/', '/upload/f_auto,q_auto/') : data.secure_url;
};