export interface Project {
  id: string;
  title: string;
  description: string;
  url?: string;
  liveUrl?: string;
  imageUrl?: string;
}

export interface Experience {
  id: string;
  role: string;
  company: string;
  duration: string; // Single field for user to enter any date format they want
  description?: string;
  location?: string;
  companyUrl?: string;
}

export interface Skill {
  id: string;
  name: string;
  category?: 'web-development' | 'mobile-development' | 'design' | 'data-science' | 'devops' | 'marketing' | 'business' | 'other';
}

export interface SocialLinks {
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
}




export interface PortfolioData {
  uid: string;
  isPublished: boolean;
  slug: string;
  templateId: string;
  userName: string;
  profession: string;
  about?: string;
  bio?: string; // Bio field for themes
  qualifications?: string; // Qualifications field for about section
  qualification1?: string; // First qualification
  qualification2?: string; // Second qualification
  qualification3?: string; // Third qualification
  qualification4?: string; // Fourth qualification
  profileImageUrl?: string;
  resumeUrl?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
  socials: SocialLinks;
  contactEmail: string;
  email?: string; // Contact email field
  phone?: string; // Phone field
  githubUrl?: string; // Direct GitHub URL
  linkedinUrl?: string; // Direct LinkedIn URL
  twitterUrl?: string; // Direct Twitter URL
}

export const defaultPortfolioData = (uid: string, email: string | null): PortfolioData => ({
  uid,
  isPublished: false,
  slug: uid, // Default slug is the user's ID
  templateId: 'creative-theme-v1',
  userName: 'Your Name',
  profession: 'Your Profession',
  contactEmail: email || '<EMAIL>',
  email: email || undefined, // Pre-fill email from Google auth
  profileImageUrl: '',
  resumeUrl: '',
  projects: [
    {
      id: 'default-project',
      title: 'Sample Project',
      description: 'This is a sample project. Click to edit and add your own project details, technologies used, and achievements.',
      url: 'https://github.com/yourusername/project',
      liveUrl: 'https://your-project-demo.com'
    }
  ],
  experiences: [
    {
      id: 'default-experience',
      role: 'Software Developer',
      company: 'Tech Company',
      duration: '2022 - Present',
      description: 'Developing innovative solutions and contributing to exciting projects. Click to edit and add your experience details.',
      location: 'Remote'
    }
  ],
  skills: [
    {
      id: 'default-skill',
      name: 'JavaScript',
      category: 'web-development'
    }
  ],
  socials: {
    githubUrl: 'https://github.com',
    linkedinUrl: 'https://linkedin.com',
    twitterUrl: 'https://twitter.com'
  },
  qualification1: "Bachelor's Degree in Computer Science",
  qualification2: "5+ years of professional experience",
});


// A type for upload mutations, makes passing it easier
export type UploadFunction = (vars: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;

export interface ProfolifyThemeProps {
  isEditing: boolean;
  serverData?: PortfolioData; // For public, server-rendered pages
  onImageUpload?: UploadFunction; // For editor page
}

// Props for each section component
export interface SectionProps {
  isEditing: boolean;
  serverData?: PortfolioData;
  onImageUpload?: UploadFunction;
}