/**
 * Theme Management Utility
 * 
 * This utility provides functions for managing themes, including:
 * - CSS file synchronization between source and public directories
 * - Theme validation and registration
 * - Build-time theme processing
 */

import fs from 'fs';
import path from 'path';
import { THEME_REGISTRY, validateThemeConfig } from '@/themes/theme-registry';

/**
 * Sync CSS files from themes directory to public directory
 * This ensures the public CSS files are always up-to-date with source files
 */
export async function syncThemeCssFiles(): Promise<void> {
  console.log('🎨 Syncing theme CSS files...');
  
  for (const theme of THEME_REGISTRY) {
    try {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      // Ensure public directory exists
      const publicDir = path.dirname(publicPath);
      if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
      }
      
      // Copy source CSS to public directory
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, publicPath);
        console.log(`✅ Synced ${theme.name}: ${theme.sourceCssFile} → public${theme.cssFile}`);
      } else {
        console.warn(`⚠️  Source CSS file not found for ${theme.name}: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`❌ Failed to sync CSS for ${theme.name}:`, error);
    }
  }
  
  console.log('✨ Theme CSS sync completed!');
}

/**
 * Validate all registered themes
 */
export function validateAllThemes(): { valid: boolean; errors: Record<string, string[]> } {
  const errors: Record<string, string[]> = {};
  let hasErrors = false;
  
  for (const theme of THEME_REGISTRY) {
    const themeErrors = validateThemeConfig(theme);
    if (themeErrors.length > 0) {
      errors[theme.id] = themeErrors;
      hasErrors = true;
    }
  }
  
  return { valid: !hasErrors, errors };
}

/**
 * Get theme file structure for a given theme ID
 */
export function getThemeStructure(themeId: string): {
  themeDir: string;
  componentsDir: string;
  cssFile: string;
  publicCssFile: string;
} | null {
  const theme = THEME_REGISTRY.find(t => t.id === themeId);
  if (!theme) return null;
  
  const themeDir = path.dirname(theme.sourceCssFile);
  
  return {
    themeDir,
    componentsDir: path.join(themeDir, 'components'),
    cssFile: theme.sourceCssFile,
    publicCssFile: path.join('public', theme.cssFile),
  };
}

/**
 * Create a new theme template structure
 */
export async function createThemeTemplate(
  themeId: string,
  themeName: string
): Promise<void> {
  const themeDir = `themes/${themeId}`;
  const componentsDir = `${themeDir}/components`;
  
  // Create directories
  if (!fs.existsSync(themeDir)) {
    fs.mkdirSync(themeDir, { recursive: true });
  }
  if (!fs.existsSync(componentsDir)) {
    fs.mkdirSync(componentsDir, { recursive: true });
  }
  
  // Create basic CSS file
  const cssContent = `/* ${themeName} Theme CSS */
/* Base styles and reset */
.theme-${themeId}-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #111827;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: white;
  overflow-x: hidden;
}

.theme-${themeId}-root *,
.theme-${themeId}-root *::before,
.theme-${themeId}-root *::after {
  box-sizing: inherit;
}

/* Add your theme styles here */
`;
  
  fs.writeFileSync(`${themeDir}/${themeId}.css`, cssContent);
  
  // Create basic theme component
  const componentContent = `"use client";
import { ProfolifyThemeProps } from "@/lib/types";

export function ${themeName.replace(/\s+/g, '')}Theme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-${themeId}-root">
            {/* Add your theme components here */}
            <div className="theme-${themeId}-container">
                <h1>Welcome to ${themeName} Theme</h1>
                <p>Start building your theme components here!</p>
            </div>
        </div>
    );
}
`;
  
  fs.writeFileSync(`${componentsDir}/${themeName.replace(/\s+/g, '')}Theme.tsx`, componentContent);
  
  console.log(`✅ Created theme template: ${themeId}`);
  console.log(`📁 Theme directory: ${themeDir}`);
  console.log(`🎨 CSS file: ${themeDir}/${themeId}.css`);
  console.log(`⚛️  Component: ${componentsDir}/${themeName.replace(/\s+/g, '')}Theme.tsx`);
  console.log(`\n📝 Next steps:`);
  console.log(`1. Add your theme to themes/theme-registry.ts`);
  console.log(`2. Build your theme components`);
  console.log(`3. Run 'npm run sync-themes' to sync CSS files`);
}

/**
 * Check if theme CSS files are in sync
 */
export function checkThemeSyncStatus(): { inSync: boolean; outdated: string[] } {
  const outdated: string[] = [];
  
  for (const theme of THEME_REGISTRY) {
    try {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      if (!fs.existsSync(sourcePath) || !fs.existsSync(publicPath)) {
        outdated.push(theme.id);
        continue;
      }
      
      const sourceStats = fs.statSync(sourcePath);
      const publicStats = fs.statSync(publicPath);
      
      if (sourceStats.mtime > publicStats.mtime) {
        outdated.push(theme.id);
      }
    } catch {
      outdated.push(theme.id);
    }
  }
  
  return { inSync: outdated.length === 0, outdated };
}
